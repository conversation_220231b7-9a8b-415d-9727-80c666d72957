# اختبار سريع للبدائل المحسنة لـ Tavily
import asyncio
import time
from datetime import datetime

def print_banner():
    """طباعة شعار الاختبار"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🔍 اختبار البدائل المحسنة لـ Tavily 🔍               ║
    ║                                                              ║
    ║  🟢 Search1API - بديل ممتاز (20-200 طلب/يوم)              ║
    ║  🦁 Brave Search - بحث مجاني قوي (5000 طلب/شهر)          ║
    ║  🚀 نظام تبديل تلقائي ذكي                                  ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def test_search1api():
    """اختبار Search1API"""
    print("\n🟢 اختبار Search1API...")
    print("-" * 40)
    
    try:
        from modules.search1api_search import search1api_search
        
        # فحص التوفر
        if not search1api_search.enabled:
            print("❌ Search1API غير مفعل")
            print("💡 أضف مفاتيح API في ملف .env:")
            print("   SEARCH1API_KEY_1=your_key_here")
            return False
            
        print(f"✅ Search1API مفعل مع {len(search1api_search.api_keys)} مفتاح")
        
        # اختبار الاتصال
        connection_test = await search1api_search.test_connection()
        if connection_test['success']:
            print(f"✅ اختبار الاتصال نجح - {connection_test['results_count']} نتيجة")
        else:
            print(f"❌ اختبار الاتصال فشل: {connection_test['error']}")
            return False
            
        # اختبار بحث سريع
        start_time = time.time()
        results = await search1api_search.search("gaming news", max_results=3)
        processing_time = time.time() - start_time
        
        print(f"🔍 البحث: {len(results)} نتيجة في {processing_time:.2f}ث")
        
        # عرض عينة من النتائج
        if results:
            print("📋 عينة من النتائج:")
            for i, result in enumerate(results[:2]):
                title = result.get('title', 'بلا عنوان')[:50]
                print(f"   {i+1}. {title}...")
                
        # إحصائيات الاستخدام
        stats = search1api_search.get_usage_statistics()
        print(f"📊 الاستخدام اليومي: {stats['daily_usage']}/{stats['daily_limit']}")
        print(f"📊 معدل النجاح: {stats['success_rate']}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في Search1API: {e}")
        return False

async def test_brave_search():
    """اختبار Brave Search"""
    print("\n🦁 اختبار Brave Search...")
    print("-" * 40)
    
    try:
        from modules.advanced_web_scraper import advanced_web_scraper
        from config.settings import BotConfig
        
        # فحص توفر المفتاح
        if not hasattr(BotConfig, 'BRAVE_SEARCH_KEY') or not BotConfig.BRAVE_SEARCH_KEY:
            print("❌ Brave Search غير مفعل")
            print("💡 أضف مفتاح API في ملف .env:")
            print("   BRAVE_SEARCH_KEY=your_key_here")
            return False
            
        print("✅ Brave Search مفعل")
        
        # اختبار بحث سريع
        start_time = time.time()
        results = await advanced_web_scraper._search_with_brave("gaming news", 3)
        processing_time = time.time() - start_time
        
        print(f"🔍 البحث: {len(results)} نتيجة في {processing_time:.2f}ث")
        
        # عرض عينة من النتائج
        if results:
            print("📋 عينة من النتائج:")
            for i, result in enumerate(results[:2]):
                title = result.get('title', 'بلا عنوان')[:50]
                print(f"   {i+1}. {title}...")
        else:
            print("⚠️ لم يتم العثور على نتائج")
            
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ خطأ في Brave Search: {e}")
        return False

async def test_tavily_fallback():
    """اختبار نظام التبديل التلقائي"""
    print("\n🔄 اختبار نظام التبديل التلقائي...")
    print("-" * 40)
    
    try:
        from modules.advanced_search_manager import AdvancedSearchManager
        
        manager = AdvancedSearchManager()
        
        # عرض المحركات المتاحة
        available_engines = list(manager.search_engines.keys())
        print(f"🔧 المحركات المتاحة: {len(available_engines)}")
        
        for engine in available_engines:
            config = manager.search_engines[engine]
            status = "✅" if config['enabled'] else "❌"
            print(f"   {status} {engine.value} (أولوية: {config['priority']})")
            
        # اختبار البحث التلقائي
        start_time = time.time()
        results = await manager.intelligent_search("gaming news", max_results=5)
        processing_time = time.time() - start_time
        
        print(f"🔍 البحث التلقائي: {len(results)} نتيجة في {processing_time:.2f}ث")
        
        if results:
            print("📋 عينة من النتائج:")
            for i, result in enumerate(results[:2]):
                title = result.get('title', 'بلا عنوان')[:50]
                source = result.get('source', 'غير محدد')
                print(f"   {i+1}. {title}... ({source})")
                
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ خطأ في نظام التبديل: {e}")
        return False

async def test_performance_comparison():
    """مقارنة أداء الخدمات"""
    print("\n⚡ مقارنة الأداء...")
    print("-" * 40)
    
    test_query = "PlayStation 5 games"
    results = {}
    
    # اختبار Search1API
    try:
        from modules.search1api_search import search1api_search
        if search1api_search.enabled:
            start_time = time.time()
            search1_results = await search1api_search.search(test_query, max_results=3)
            search1_time = time.time() - start_time
            results['Search1API'] = {
                'time': search1_time,
                'count': len(search1_results),
                'speed': len(search1_results) / max(search1_time, 0.1)
            }
    except Exception as e:
        print(f"⚠️ تخطي Search1API: {e}")
        
    # اختبار Brave Search
    try:
        from modules.advanced_web_scraper import advanced_web_scraper
        from config.settings import BotConfig
        
        if hasattr(BotConfig, 'BRAVE_SEARCH_KEY') and BotConfig.BRAVE_SEARCH_KEY:
            start_time = time.time()
            brave_results = await advanced_web_scraper._search_with_brave(test_query, 3)
            brave_time = time.time() - start_time
            results['Brave Search'] = {
                'time': brave_time,
                'count': len(brave_results),
                'speed': len(brave_results) / max(brave_time, 0.1)
            }
    except Exception as e:
        print(f"⚠️ تخطي Brave Search: {e}")
        
    # عرض النتائج
    if results:
        print("📊 نتائج المقارنة:")
        for service, data in results.items():
            print(f"   {service}: {data['count']} نتيجة في {data['time']:.2f}ث (سرعة: {data['speed']:.1f})")
            
        # أفضل خدمة
        best_service = max(results.items(), key=lambda x: x[1]['speed'])
        print(f"🏆 أفضل أداء: {best_service[0]}")
    else:
        print("⚠️ لم يتم اختبار أي خدمة")

def print_setup_guide():
    """طباعة دليل الإعداد"""
    print("\n📖 دليل الإعداد السريع:")
    print("=" * 50)
    
    print("1. 🟢 Search1API (بديل ممتاز لـ Tavily):")
    print("   • اذهب إلى: https://search1api.com")
    print("   • أنشئ حساب مجاني")
    print("   • احصل على API Key")
    print("   • أضف إلى .env: SEARCH1API_KEY_1=your_key")
    print("")
    
    print("2. 🦁 Brave Search (بحث مجاني قوي):")
    print("   • اذهب إلى: https://brave.com/search/api/")
    print("   • أنشئ حساب مجاني")
    print("   • اختر Free AI Plan (5000 طلب/شهر)")
    print("   • أضف إلى .env: BRAVE_SEARCH_KEY=your_key")
    print("")
    
    print("3. 🔄 تشغيل الاختبار:")
    print("   python quick_test_search_alternatives.py")

def print_summary(search1_ok, brave_ok, fallback_ok):
    """طباعة ملخص النتائج"""
    print("\n📋 ملخص النتائج:")
    print("=" * 50)
    
    total_tests = 3
    passed_tests = sum([search1_ok, brave_ok, fallback_ok])
    
    print(f"📊 إجمالي الاختبارات: {total_tests}")
    print(f"✅ الاختبارات الناجحة: {passed_tests}")
    print(f"❌ الاختبارات الفاشلة: {total_tests - passed_tests}")
    print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n🎯 الحالة:")
    print(f"   Search1API: {'✅ يعمل' if search1_ok else '❌ لا يعمل'}")
    print(f"   Brave Search: {'✅ يعمل' if brave_ok else '❌ لا يعمل'}")
    print(f"   نظام التبديل: {'✅ يعمل' if fallback_ok else '❌ لا يعمل'}")
    
    print("\n💡 التوصيات:")
    if search1_ok and brave_ok:
        print("🎉 ممتاز! جميع البدائل تعمل بشكل مثالي")
        print("🚀 النظام جاهز للاستخدام مع موثوقية عالية")
    elif search1_ok or brave_ok:
        print("👍 جيد! بعض البدائل تعمل")
        if not search1_ok:
            print("💡 أضف مفاتيح Search1API لتحسين الموثوقية")
        if not brave_ok:
            print("💡 أضف مفتاح Brave Search لتحسين الموثوقية")
    else:
        print("⚠️ لا توجد بدائل تعمل - راجع الإعداد")
        print("📚 راجع الدليل: ENHANCED_SEARCH_ALTERNATIVES_GUIDE.md")

async def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print(f"🕐 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # تشغيل الاختبارات
    search1_ok = await test_search1api()
    brave_ok = await test_brave_search()
    fallback_ok = await test_tavily_fallback()
    
    # مقارنة الأداء
    await test_performance_comparison()
    
    # طباعة الملخص
    print_summary(search1_ok, brave_ok, fallback_ok)
    
    # دليل الإعداد إذا لزم الأمر
    if not (search1_ok and brave_ok):
        print_setup_guide()
    
    print(f"\n🎉 اكتمل الاختبار السريع!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        print("📚 راجع الدليل للمساعدة: ENHANCED_SEARCH_ALTERNATIVES_GUIDE.md")
