# اختبار البدائل المحسنة لـ Tavily (Brave Search و Search1API)
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any

from modules.search1api_search import search1api_search
from modules.advanced_search_manager import AdvancedSearchManager, SearchEngine
from modules.logger import logger

class EnhancedSearchAlternativesTester:
    """اختبار البدائل المحسنة لـ Tavily"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
        # استعلامات اختبار متنوعة
        self.test_queries = [
            "gaming news today",
            "new video game releases 2025",
            "PlayStation 5 games",
            "Xbox Series X reviews",
            "Nintendo Switch updates",
            "PC gaming hardware",
            "mobile gaming trends",
            "esports tournaments"
        ]
        
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للبدائل"""
        print("🔍 بدء اختبار البدائل المحسنة لـ Tavily")
        print("=" * 60)
        
        try:
            # 1. اختب<PERSON>ر Search1API
            await self._test_search1api()
            
            # 2. اختبار Brave Search
            await self._test_brave_search()
            
            # 3. اختبار مدير البحث المتقدم
            await self._test_advanced_search_manager()
            
            # 4. اختبار الأداء المقارن
            await self._test_performance_comparison()
            
            # 5. إنشاء التقرير
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
            print(f"❌ فشل الاختبار: {e}")
            
    async def _test_search1api(self):
        """اختبار Search1API"""
        print("\n🔍 اختبار Search1API...")
        print("-" * 30)
        
        try:
            # فحص التوفر
            if not search1api_search.enabled:
                print("⚠️ Search1API غير مفعل - لا توجد مفاتيح API")
                self.test_results['search1api'] = {
                    'status': 'disabled',
                    'reason': 'No API keys configured'
                }
                return
                
            # اختبار الاتصال
            connection_test = await search1api_search.test_connection()
            print(f"🔗 اختبار الاتصال: {'✅ نجح' if connection_test['success'] else '❌ فشل'}")
            
            if not connection_test['success']:
                print(f"   خطأ: {connection_test['error']}")
                self.test_results['search1api'] = {
                    'status': 'connection_failed',
                    'error': connection_test['error']
                }
                return
                
            # اختبار البحث العادي
            test_query = self.test_queries[0]
            start_time = time.time()
            
            results = await search1api_search.search(test_query, max_results=5)
            processing_time = time.time() - start_time
            
            print(f"🔍 البحث العادي: {len(results)} نتيجة في {processing_time:.2f}ث")
            
            # اختبار البحث في الأخبار
            news_results = await search1api_search.search_news(test_query, max_results=3)
            print(f"📰 البحث في الأخبار: {len(news_results)} نتيجة")
            
            # اختبار البحث المتخصص في الألعاب
            gaming_results = await search1api_search.search_gaming_content(test_query, max_results=3)
            print(f"🎮 البحث المتخصص: {len(gaming_results)} نتيجة")
            
            # الحصول على الإحصائيات
            stats = search1api_search.get_usage_statistics()
            print(f"📊 الاستخدام اليومي: {stats['daily_usage']}/{stats['daily_limit']}")
            print(f"📊 معدل النجاح: {stats['success_rate']}%")
            
            self.test_results['search1api'] = {
                'status': 'success',
                'results_count': len(results),
                'news_results_count': len(news_results),
                'gaming_results_count': len(gaming_results),
                'processing_time': processing_time,
                'daily_usage': stats['daily_usage'],
                'success_rate': stats['success_rate']
            }
            
        except Exception as e:
            print(f"❌ خطأ في اختبار Search1API: {e}")
            self.test_results['search1api'] = {
                'status': 'error',
                'error': str(e)
            }
            
    async def _test_brave_search(self):
        """اختبار Brave Search"""
        print("\n🦁 اختبار Brave Search...")
        print("-" * 30)
        
        try:
            from modules.advanced_web_scraper import advanced_web_scraper
            from config.settings import BotConfig
            
            # فحص توفر المفتاح
            if not hasattr(BotConfig, 'BRAVE_SEARCH_KEY') or not BotConfig.BRAVE_SEARCH_KEY:
                print("⚠️ Brave Search غير مفعل - لا يوجد مفتاح API")
                self.test_results['brave_search'] = {
                    'status': 'disabled',
                    'reason': 'No API key configured'
                }
                return
                
            # اختبار البحث
            test_query = self.test_queries[1]
            start_time = time.time()
            
            results = await advanced_web_scraper._search_with_brave(test_query, 5)
            processing_time = time.time() - start_time
            
            print(f"🔍 البحث: {len(results)} نتيجة في {processing_time:.2f}ث")
            
            # عرض عينة من النتائج
            if results:
                print("📋 عينة من النتائج:")
                for i, result in enumerate(results[:3]):
                    print(f"   {i+1}. {result.get('title', 'بلا عنوان')[:50]}...")
                    
            self.test_results['brave_search'] = {
                'status': 'success',
                'results_count': len(results),
                'processing_time': processing_time,
                'sample_results': [r.get('title', '') for r in results[:3]]
            }
            
        except Exception as e:
            print(f"❌ خطأ في اختبار Brave Search: {e}")
            self.test_results['brave_search'] = {
                'status': 'error',
                'error': str(e)
            }
            
    async def _test_advanced_search_manager(self):
        """اختبار مدير البحث المتقدم"""
        print("\n🚀 اختبار مدير البحث المتقدم...")
        print("-" * 30)
        
        try:
            manager = AdvancedSearchManager()
            
            # عرض المحركات المتاحة
            available_engines = list(manager.search_engines.keys())
            print(f"🔧 المحركات المتاحة: {len(available_engines)}")
            
            for engine in available_engines:
                config = manager.search_engines[engine]
                status = "✅ مفعل" if config['enabled'] else "❌ معطل"
                print(f"   - {engine.value}: {status} (أولوية: {config['priority']})")
                
            # اختبار البحث التلقائي
            test_query = self.test_queries[2]
            start_time = time.time()
            
            results = await manager.intelligent_search(test_query, max_results=8)
            processing_time = time.time() - start_time
            
            print(f"🔍 البحث التلقائي: {len(results)} نتيجة في {processing_time:.2f}ث")
            
            # الحصول على إحصائيات الاستخدام
            usage_stats = manager.get_usage_statistics()
            print(f"📊 إجمالي الطلبات: {usage_stats.get('total_requests', 0)}")
            print(f"📊 الطلبات الناجحة: {usage_stats.get('successful_requests', 0)}")
            
            self.test_results['advanced_search_manager'] = {
                'status': 'success',
                'available_engines': len(available_engines),
                'results_count': len(results),
                'processing_time': processing_time,
                'total_requests': usage_stats.get('total_requests', 0)
            }
            
        except Exception as e:
            print(f"❌ خطأ في اختبار مدير البحث المتقدم: {e}")
            self.test_results['advanced_search_manager'] = {
                'status': 'error',
                'error': str(e)
            }
            
    async def _test_performance_comparison(self):
        """اختبار الأداء المقارن"""
        print("\n⚡ اختبار الأداء المقارن...")
        print("-" * 30)
        
        try:
            test_query = self.test_queries[3]
            performance_results = {}
            
            # اختبار Search1API
            if search1api_search.enabled:
                start_time = time.time()
                search1_results = await search1api_search.search(test_query, max_results=5)
                search1_time = time.time() - start_time
                
                performance_results['search1api'] = {
                    'time': search1_time,
                    'results': len(search1_results),
                    'speed_score': len(search1_results) / max(search1_time, 0.1)
                }
                print(f"🔍 Search1API: {len(search1_results)} نتيجة في {search1_time:.2f}ث")
                
            # اختبار Brave Search
            try:
                from modules.advanced_web_scraper import advanced_web_scraper
                from config.settings import BotConfig
                
                if hasattr(BotConfig, 'BRAVE_SEARCH_KEY') and BotConfig.BRAVE_SEARCH_KEY:
                    start_time = time.time()
                    brave_results = await advanced_web_scraper._search_with_brave(test_query, 5)
                    brave_time = time.time() - start_time
                    
                    performance_results['brave_search'] = {
                        'time': brave_time,
                        'results': len(brave_results),
                        'speed_score': len(brave_results) / max(brave_time, 0.1)
                    }
                    print(f"🦁 Brave Search: {len(brave_results)} نتيجة في {brave_time:.2f}ث")
                    
            except Exception as e:
                print(f"⚠️ تخطي Brave Search: {e}")
                
            # تحليل النتائج
            if performance_results:
                best_service = max(performance_results.items(), key=lambda x: x[1]['speed_score'])
                print(f"🏆 أفضل أداء: {best_service[0]} (نقاط: {best_service[1]['speed_score']:.2f})")
                
            self.test_results['performance_comparison'] = performance_results
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الأداء: {e}")
            self.test_results['performance_comparison'] = {
                'status': 'error',
                'error': str(e)
            }
            
    def _generate_test_report(self):
        """إنشاء تقرير الاختبار"""
        print("\n📋 تقرير اختبار البدائل المحسنة")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') == 'success')
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ الاختبارات الناجحة: {successful_tests}")
        print(f"❌ الاختبارات الفاشلة: {total_tests - successful_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        
        # تفاصيل كل اختبار
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            print(f"\n{status_icon} {test_name}:")
            
            if result.get('status') == 'success':
                for key, value in result.items():
                    if key != 'status':
                        print(f"   - {key}: {value}")
            else:
                print(f"   - حالة: {result.get('status', 'غير محدد')}")
                if result.get('error'):
                    print(f"   - خطأ: {result['error']}")
                if result.get('reason'):
                    print(f"   - السبب: {result['reason']}")
                    
        # توصيات
        print(f"\n💡 التوصيات:")
        
        if self.test_results.get('search1api', {}).get('status') == 'success':
            print("✅ Search1API يعمل بشكل ممتاز - يُنصح باستخدامه كبديل أول لـ Tavily")
        else:
            print("⚠️ Search1API غير متاح - تأكد من إضافة مفاتيح API")
            
        if self.test_results.get('brave_search', {}).get('status') == 'success':
            print("✅ Brave Search يعمل بشكل جيد - يُنصح باستخدامه كبديل ثاني")
        else:
            print("⚠️ Brave Search غير متاح - تأكد من إضافة مفتاح API")
            
        if self.test_results.get('advanced_search_manager', {}).get('status') == 'success':
            print("✅ مدير البحث المتقدم يعمل بشكل ممتاز")
        else:
            print("⚠️ مدير البحث المتقدم يحتاج مراجعة")
            
        print(f"\n🎉 اكتمل اختبار البدائل المحسنة!")
        print(f"⏱️ مدة الاختبار: {(datetime.now() - self.start_time).total_seconds():.1f} ثانية")

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = EnhancedSearchAlternativesTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
