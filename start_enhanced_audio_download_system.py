# تشغيل سريع لنظام تحميل الصوت المحسن
import asyncio
import threading
import time
import webbrowser
from datetime import datetime

from modules.enhanced_audio_downloader import enhanced_audio_downloader
from modules.apify_downloader import apify_downloader
from modules.youtube_dl_downloader import youtube_dl_downloader
from modules.logger import logger

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎵 نظام تحميل الصوت المحسن 🎵                       ║
    ║                                                              ║
    ║  🔵 Apify YouTube Downloader - جودة عالية                  ║
    ║  🟠 YouTube-DL / YT-DLP - مجاني بلا حدود                   ║
    ║  🧠 نظام أولوية ذكي                                        ║
    ║  🌐 واجهة ويب تفاعلية                                      ║
    ║  🔄 تبديل تلقائي للطرق                                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_methods_info():
    """طباعة معلومات طرق التحميل المتاحة"""
    print("\n🎯 طرق التحميل المتاحة:")
    print("=" * 50)
    
    methods_info = [
        ("Apify YouTube Downloader", "$5 شهرياً مجاني", "⭐⭐⭐⭐⭐", "🔵"),
        ("YouTube-DL", "مجاني بلا حدود", "⭐⭐⭐⭐", "🟠"),
        ("YT-DLP", "مجاني بلا حدود", "⭐⭐⭐⭐⭐", "🟠"),
        ("PyTube", "بديل احتياطي", "⭐⭐⭐", "🔄"),
        ("YouTube API", "بديل احتياطي", "⭐⭐", "🔄")
    ]
    
    for method, limit, rating, icon in methods_info:
        print(f"  {icon} {rating} {method:<20} - {limit}")

def print_quick_start_guide():
    """طباعة دليل البدء السريع"""
    print("\n📖 دليل البدء السريع:")
    print("=" * 50)
    print("1. 🔑 أضف APIFY_API_TOKEN إلى ملف .env (اختياري)")
    print("2. 🛠️ تأكد من تثبيت youtube-dl و ffmpeg")
    print("3. 🌐 افتح واجهة الويب: http://localhost:5002")
    print("4. 🧪 شغل الاختبار: python test_enhanced_audio_download_system.py")
    print("5. 📚 راجع الدليل: ENHANCED_AUDIO_DOWNLOAD_GUIDE.md")

def check_tools_availability():
    """فحص توفر الأدوات"""
    print("\n🔧 فحص توفر الأدوات:")
    print("-" * 30)
    
    try:
        availability = enhanced_audio_downloader.get_method_availability()
        
        tools = [
            ("Apify API", availability.get('apify', False)),
            ("YouTube-DL", availability.get('youtube_dl', False)),
            ("YT-DLP", availability.get('yt_dlp', False)),
            ("FFmpeg", availability.get('ffmpeg', False))
        ]
        
        for tool, available in tools:
            status = "✅ متوفر" if available else "❌ غير متوفر"
            print(f"  {tool:<15}: {status}")
            
        # معلومات إضافية عن Apify
        if apify_downloader.is_available():
            credit = apify_downloader.get_remaining_credit()
            print(f"  Apify Credit   : ${credit:.2f} متبقي")
        
        return any(availability.values())
        
    except Exception as e:
        print(f"❌ خطأ في فحص الأدوات: {e}")
        return False

def start_web_dashboard():
    """تشغيل واجهة الويب"""
    try:
        from audio_download_dashboard import run_dashboard
        
        print("\n🌐 بدء تشغيل واجهة الويب...")
        
        # تشغيل في thread منفصل
        dashboard_thread = threading.Thread(
            target=run_dashboard,
            kwargs={'host': '0.0.0.0', 'port': 5002, 'debug': False},
            daemon=True
        )
        dashboard_thread.start()
        
        # انتظار قليل ثم فتح المتصفح
        time.sleep(3)
        try:
            webbrowser.open('http://localhost:5002')
            print("✅ تم فتح واجهة الويب في المتصفح")
        except:
            print("⚠️ لم يتمكن من فتح المتصفح تلقائياً")
            print("🔗 افتح المتصفح يدوياً على: http://localhost:5002")
            
        return True
        
    except Exception as e:
        print(f"❌ فشل في تشغيل واجهة الويب: {e}")
        return False

async def test_system_quickly():
    """اختبار سريع للنظام"""
    print("\n🧪 اختبار سريع للنظام...")
    print("-" * 30)
    
    try:
        # فحص تهيئة النظام
        stats = enhanced_audio_downloader.get_download_statistics()
        
        total_methods = stats['total_methods']
        active_methods = stats['active_methods']
        
        print(f"✅ إجمالي طرق التحميل: {total_methods}")
        print(f"✅ الطرق النشطة: {active_methods}")
        
        # عرض حالة كل طريقة
        print("\n📊 حالة طرق التحميل:")
        for method_name, method_data in stats['methods'].items():
            status_icon = "✅" if method_data['status'] == 'active' else "❌"
            method_display = {
                'apify': 'Apify',
                'youtube_dl': 'YouTube-DL',
                'yt_dlp': 'YT-DLP',
                'pytube': 'PyTube',
                'youtube_api': 'YouTube API'
            }.get(method_name, method_name)
            
            print(f"  {status_icon} {method_display}: {method_data['status']}")
            
        return True
        
    except Exception as e:
        print(f"❌ فشل في الاختبار السريع: {e}")
        return False

async def run_demo():
    """تشغيل عرض توضيحي"""
    print("\n🎬 عرض توضيحي للنظام...")
    print("-" * 30)
    
    try:
        # فيديو اختبار قصير
        test_video_id = "dQw4w9WgXcQ"  # Rick Roll - فيديو قصير مشهور
        test_video_url = f"https://www.youtube.com/watch?v={test_video_id}"
        
        print(f"🔄 محاولة تحميل فيديو اختبار: {test_video_id}")
        
        result = await enhanced_audio_downloader.download_audio(
            video_id=test_video_id,
            video_url=test_video_url,
            quality="low",  # جودة منخفضة للسرعة
            format="mp3"
        )
        
        if result.success:
            print(f"✅ نجح التحميل!")
            print(f"🎯 الطريقة المستخدمة: {result.method_used}")
            print(f"📁 حجم الملف: {result.file_size} بايت")
            print(f"⏱️ وقت المعالجة: {result.processing_time:.1f}ث")
            
            # حذف الملف التجريبي
            if result.file_path and os.path.exists(result.file_path):
                os.remove(result.file_path)
                print("🗑️ تم حذف الملف التجريبي")
        else:
            print(f"❌ فشل التحميل: {result.error_message}")
            
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")

def print_usage_tips():
    """طباعة نصائح الاستخدام"""
    print("\n💡 نصائح للاستخدام الأمثل:")
    print("=" * 50)
    print("• استخدم النظام المحسن بدلاً من الطرق التقليدية")
    print("• راقب استخدام Apify عبر واجهة الويب")
    print("• استخدم جودة منخفضة للسرعة، عالية للجودة")
    print("• تأكد من تحديث youtube-dl و yt-dlp دورياً")
    print("• شغل الاختبار الشامل دورياً")

def print_installation_guide():
    """تذكير بدليل التثبيت"""
    print("\n🛠️ دليل التثبيت السريع:")
    print("=" * 50)
    print("1. تثبيت YouTube-DL:")
    print("   pip install youtube-dl")
    print("   # أو الأفضل:")
    print("   pip install yt-dlp")
    print("")
    print("2. تثبيت FFmpeg:")
    print("   # Ubuntu/Debian:")
    print("   sudo apt install ffmpeg")
    print("   # macOS:")
    print("   brew install ffmpeg")
    print("")
    print("3. إعداد Apify (اختياري):")
    print("   # أضف إلى .env:")
    print("   APIFY_API_TOKEN=your_token_here")
    print("")
    print("📚 للتفاصيل الكاملة: ENHANCED_AUDIO_DOWNLOAD_GUIDE.md")

async def main():
    """الدالة الرئيسية"""
    print_banner()
    print_methods_info()
    print_quick_start_guide()
    print_installation_guide()
    
    print(f"\n🕐 وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # فحص توفر الأدوات
    tools_available = check_tools_availability()
    
    if tools_available:
        print("\n✅ النظام جاهز للاستخدام!")
        
        # اختبار سريع للنظام
        system_ok = await test_system_quickly()
        
        if system_ok:
            # تشغيل واجهة الويب
            web_started = start_web_dashboard()
            
            if web_started:
                print("\n🎯 الخيارات المتاحة:")
                print("1. 🌐 واجهة الويب: http://localhost:5002")
                print("2. 🧪 اختبار شامل: python test_enhanced_audio_download_system.py")
                print("3. 🎬 عرض توضيحي: سيتم تشغيله الآن...")
                
                # تشغيل عرض توضيحي
                await run_demo()
                
                print_usage_tips()
                
                print("\n🎉 النظام يعمل بنجاح!")
                print("⏹️ اضغط Ctrl+C للإيقاف")
                
                try:
                    # إبقاء النظام يعمل
                    while True:
                        await asyncio.sleep(60)
                        print(f"💓 النظام يعمل - {datetime.now().strftime('%H:%M:%S')}")
                        
                except KeyboardInterrupt:
                    print("\n🛑 تم إيقاف النظام بواسطة المستخدم")
                    
            else:
                print("❌ فشل في تشغيل واجهة الويب")
                
        else:
            print("❌ النظام غير جاهز - راجع الإعدادات")
            
    else:
        print("❌ لا توجد أدوات متاحة - راجع دليل التثبيت")
        
    print("\n👋 شكراً لاستخدام نظام تحميل الصوت المحسن!")

if __name__ == "__main__":
    import os
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("📚 راجع الدليل للمساعدة: ENHANCED_AUDIO_DOWNLOAD_GUIDE.md")
