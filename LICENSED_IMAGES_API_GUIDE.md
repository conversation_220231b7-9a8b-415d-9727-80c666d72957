# 🎨 دليل شامل للحصول على مفاتيح API للصور المرخصة

## نظرة عامة

هذا الدليل يشرح كيفية الحصول على مفاتيح API لجميع مصادر الصور المرخصة المدعومة في الوكيل الذكي. جميع هذه المصادر توفر صور مجانية ومرخصة للاستخدام التجاري.

---

## 🆓 المصادر المجانية للصور العامة

### 1. Unsplash API ⭐⭐⭐⭐⭐

**الوصف**: أكبر مكتبة صور مجانية عالية الجودة في العالم

**المميزات**:
- ✅ مجاني تماماً
- ✅ صور عالية الجودة (4K+)
- ✅ ترخيص Unsplash (استخدام تجاري مجاني)
- ✅ 50,000 طلب/ساعة
- ✅ لا يتطلب attribution إجباري

**كيفية الحصول على المفتاح**:

1. **إنشاء حساب**:
   - اذهب إلى: https://unsplash.com/developers
   - انقر على "Register as a developer"
   - أنشئ حساب جديد أو سجل دخول

2. **إنشاء تطبيق جديد**:
   - انقر على "New Application"
   - اقبل شروط الاستخدام
   - املأ المعلومات:
     - **Application name**: Gaming News Bot
     - **Description**: AI bot for gaming news with licensed images
     - **Website**: موقعك الإلكتروني (أو ضع https://example.com)

3. **الحصول على المفتاح**:
   - ستحصل على **Access Key** و **Secret Key**
   - انسخ **Access Key** فقط (هذا ما نحتاجه)

4. **إضافة المفتاح للوكيل**:
   ```env
   UNSPLASH_ACCESS_KEY=your_access_key_here
   ```

**حدود الاستخدام**:
- Demo: 50 طلب/ساعة
- Production: 5,000 طلب/ساعة (بعد التقديم)

---

### 2. Pexels API ⭐⭐⭐⭐⭐

**الوصف**: مكتبة ضخمة من الصور والفيديوهات المجانية

**المميزات**:
- ✅ مجاني تماماً
- ✅ صور عالية الجودة
- ✅ ترخيص Pexels (استخدام تجاري مجاني)
- ✅ 200 طلب/ساعة، 20,000 طلب/شهر
- ✅ لا يتطلب attribution إجباري

**كيفية الحصول على المفتاح**:

1. **إنشاء حساب**:
   - اذهب إلى: https://www.pexels.com/api/
   - انقر على "Get Started"
   - أنشئ حساب جديد

2. **طلب API Key**:
   - اذهب إلى: https://www.pexels.com/api/new/
   - املأ النموذج:
     - **First Name**: اسمك الأول
     - **Last Name**: اسمك الأخير
     - **Email**: بريدك الإلكتروني
     - **Project Description**: Gaming news website with AI content generation
     - **Project URL**: موقعك (أو https://example.com)

3. **الحصول على المفتاح**:
   - ستصلك رسالة تأكيد على البريد الإلكتروني
   - انقر على الرابط للتفعيل
   - ستجد API Key في لوحة التحكم

4. **إضافة المفتاح للوكيل**:
   ```env
   PEXELS_API_KEY=your_api_key_here
   ```

---

### 3. Pixabay API ⭐⭐⭐⭐

**الوصف**: مكتبة كبيرة من الصور والرسوم المجانية

**المميزات**:
- ✅ مجاني تماماً
- ✅ صور ورسوم متنوعة
- ✅ ترخيص Pixabay (استخدام تجاري مجاني)
- ✅ 5,000 طلب/ساعة
- ✅ لا يتطلب attribution إجباري

**كيفية الحصول على المفتاح**:

1. **إنشاء حساب**:
   - اذهب إلى: https://pixabay.com/api/docs/
   - انقر على "Get API Key"
   - أنشئ حساب جديد أو سجل دخول

2. **الحصول على المفتاح**:
   - بعد تسجيل الدخول، اذهب إلى: https://pixabay.com/api/docs/
   - ستجد API Key الخاص بك مباشرة في الصفحة

3. **إضافة المفتاح للوكيل**:
   ```env
   PIXABAY_API_KEY=your_api_key_here
   ```

---

## 🎮 المصادر المتخصصة في الألعاب

### 4. Giant Bomb API ⭐⭐⭐⭐

**الوصف**: قاعدة بيانات شاملة للألعاب مع صور رسمية

**المميزات**:
- ✅ مجاني
- ✅ صور ألعاب رسمية عالية الجودة
- ✅ معلومات شاملة عن الألعاب
- ✅ 400 طلب/ساعة
- ✅ مصدر موثوق (CBS Interactive)

**كيفية الحصول على المفتاح**:

1. **إنشاء حساب**:
   - اذهب إلى: https://www.giantbomb.com/api/
   - انقر على "Get an API Key"
   - أنشئ حساب جديد

2. **طلب API Key**:
   - بعد تسجيل الدخول، اذهب إلى: https://www.giantbomb.com/api/
   - انقر على "Generate New API Key"
   - املأ المعلومات المطلوبة

3. **إضافة المفتاح للوكيل**:
   ```env
   GIANT_BOMB_API_KEY=your_api_key_here
   ```

---

### 5. MobyGames API ⭐⭐⭐

**الوصف**: أكبر قاعدة بيانات للألعاب التاريخية

**المميزات**:
- ✅ مجاني للاستخدام الشخصي
- ✅ صور ألعاب تاريخية نادرة
- ✅ معلومات مفصلة عن الألعاب القديمة
- ✅ 360 طلب/ساعة

**كيفية الحصول على المفتاح**:

1. **إنشاء حساب**:
   - اذهب إلى: https://www.mobygames.com/info/api/
   - انقر على "Request API Access"
   - أنشئ حساب جديد

2. **طلب الوصول**:
   - املأ نموذج طلب الوصول
   - اشرح أنك تستخدمه لموقع أخبار الألعاب
   - انتظر الموافقة (عادة 1-3 أيام)

3. **إضافة المفتاح للوكيل**:
   ```env
   MOBYGAMES_API_KEY=your_api_key_here
   ```

---

## 🔧 إعداد المفاتيح في الوكيل

### ملف .env

أضف جميع المفاتيح إلى ملف `.env`:

```env
# مصادر الصور المجانية
UNSPLASH_ACCESS_KEY=your_unsplash_key
PEXELS_API_KEY=your_pexels_key
PIXABAY_API_KEY=your_pixabay_key

# مصادر الألعاب المتخصصة
GIANT_BOMB_API_KEY=your_giantbomb_key
MOBYGAMES_API_KEY=your_mobygames_key

# المصادر الموجودة حالياً
IGDB_CLIENT_ID=your_igdb_client_id
IGDB_CLIENT_SECRET=your_igdb_client_secret
RAWG_API_KEY=your_rawg_key
```

### ترتيب الأولوية المقترح

```python
provider_priority = [
    'press_kits',      # الأولوية الأولى - صور رسمية
    'giant_bomb',      # صور ألعاب رسمية
    'igdb',           # قاعدة بيانات ألعاب شاملة
    'steam',          # صور Steam الرسمية
    'mobygames',      # ألعاب تاريخية
    'rawg',           # قاعدة بيانات حديثة
    'unsplash',       # صور عامة عالية الجودة
    'pexels',         # صور عامة متنوعة
    'pixabay'         # صور ورسوم مجانية
]
```

---

## 📊 مقارنة المصادر

| المصدر | مجاني | جودة الصور | تخصص الألعاب | حد الطلبات | سهولة الحصول |
|---------|--------|------------|---------------|-------------|---------------|
| Unsplash | ✅ | ⭐⭐⭐⭐⭐ | ⭐⭐ | 50K/ساعة | ⭐⭐⭐⭐⭐ |
| Pexels | ✅ | ⭐⭐⭐⭐⭐ | ⭐⭐ | 20K/شهر | ⭐⭐⭐⭐⭐ |
| Pixabay | ✅ | ⭐⭐⭐⭐ | ⭐⭐ | 5K/ساعة | ⭐⭐⭐⭐⭐ |
| Giant Bomb | ✅ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 400/ساعة | ⭐⭐⭐⭐ |
| MobyGames | ✅ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 360/ساعة | ⭐⭐⭐ |

---

## 🚀 الخطوات التالية

1. **احصل على المفاتيح**: ابدأ بـ Unsplash و Pexels (الأسهل)
2. **اختبر المفاتيح**: تأكد من عمل كل مفتاح
3. **راقب الاستخدام**: تتبع حدود الطلبات
4. **حسن الأداء**: اضبط ترتيب الأولوية حسب احتياجاتك

---

## 🧪 اختبار المصادر

بعد إضافة المفاتيح، يمكنك اختبار جميع المصادر:

```bash
# اختبار شامل لجميع المصادر
python test_licensed_images.py
```

**النتائج المتوقعة**:
```
🚀 اختبار مصادر الصور المرخصة الجديدة
==================================================
🔑 فحص مفاتيح API...
✅ UNSPLASH_ACCESS_KEY: متوفر
✅ PEXELS_API_KEY: متوفر
✅ PIXABAY_API_KEY: متوفر
✅ GIANT_BOMB_API_KEY: متوفر
✅ MOBYGAMES_API_KEY: متوفر

📊 المفاتيح المتوفرة: 5/5

🎨 اختبار Unsplash API...
✅ Unsplash: تم العثور على 2 صورة

🎨 اختبار Pexels API...
✅ Pexels: تم العثور على 2 صورة

🎨 اختبار Pixabay API...
✅ Pixabay: تم العثور على 2 صورة

🎮 اختبار Giant Bomb API...
✅ Giant Bomb: تم العثور على 2 صورة

🕹️ اختبار MobyGames API...
✅ MobyGames: تم العثور على 2 صورة

🎯 اختبار مدير الصور المرخصة الكامل...
✅ مدير الصور: تم العثور على 3 صورة
📊 توزيع المصادر:
   • Giant Bomb: 2 صورة
   • Unsplash: 1 صورة

==================================================
📊 ملخص النتائج:
   • Unsplash: ✅ نجح
   • Pexels: ✅ نجح
   • Pixabay: ✅ نجح
   • Giant Bomb: ✅ نجح
   • MobyGames: ✅ نجح
   • مدير الصور الكامل: ✅ نجح

🎯 النتيجة النهائية: 6/6 اختبار نجح

🎉 تم تفعيل مصادر صور جديدة بنجاح!
💡 يمكنك الآن الاستفادة من المزيد من الصور المرخصة
```

---

## 📈 إحصائيات الاستخدام

بعد التفعيل، يمكنك مراقبة استخدام المصادر:

```python
from modules.licensed_image_manager import LicensedImageManager

manager = LicensedImageManager()
stats = manager.get_usage_stats()

print(f"إجمالي الطلبات: {stats['total_requests']}")
print(f"الطلبات الناجحة: {stats['successful_requests']}")
print(f"معدل النجاح: {stats['success_rate']:.1f}%")

# إحصائيات كل مصدر
for provider, usage in stats['provider_usage'].items():
    print(f"{provider}: {usage} طلب")
```

---

## 🔄 ترتيب الأولوية الجديد

```python
# الترتيب الحالي في النظام
provider_priority = [
    'press_kits',      # 1. صور رسمية من المطورين
    'giant_bomb',      # 2. صور ألعاب رسمية من Giant Bomb
    'igdb',           # 3. قاعدة بيانات ألعاب شاملة
    'rawg',           # 4. قاعدة بيانات حديثة
    'steam',          # 5. صور Steam الرسمية
    'mobygames',      # 6. ألعاب تاريخية
    'unsplash',       # 7. صور عامة عالية الجودة
    'pexels',         # 8. صور عامة متنوعة
    'pixabay'         # 9. صور ورسوم مجانية
]
```

---

## ⚠️ نصائح مهمة

- **احفظ المفاتيح بأمان**: لا تشاركها أو تنشرها
- **راقب الحدود**: تجنب تجاوز حدود الطلبات
- **اقرأ الشروط**: تأكد من فهم شروط كل خدمة
- **استخدم التخزين المؤقت**: لتقليل عدد الطلبات
- **احترم الترخيص**: اتبع شروط الترخيص لكل صورة
- **اختبر بانتظام**: تأكد من عمل جميع المصادر
- **راقب الجودة**: تحقق من جودة الصور المحصلة
