# واجهة ويب لمراقبة نظام تحميل الصوت المحسن
from flask import Flask, render_template_string, jsonify, request
import json
import os
from datetime import datetime
import threading
import time

from modules.enhanced_audio_downloader import enhanced_audio_downloader
from modules.apify_downloader import apify_downloader
from modules.youtube_dl_downloader import youtube_dl_downloader
from modules.audio_download_priority_manager import audio_download_priority_manager
from modules.logger import logger

app = Flask(__name__)

# HTML Template للواجهة
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم تحميل الصوت المحسن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 10px;
        }
        
        .methods-section {
            padding: 30px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .method-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .method-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .method-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .method-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-rate-limited { background: #fff3cd; color: #856404; }
        .status-quota-exceeded { background: #f5c6cb; color: #721c24; }
        .status-disabled { background: #e2e3e5; color: #383d41; }
        .status-unavailable { background: #f1c0c7; color: #721c24; }
        
        .method-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #feca57);
            transition: width 0.3s ease;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        
        .btn-primary:hover {
            background: #ff5252;
        }
        
        .btn-warning {
            background: #feca57;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #ff9ff3;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            background: #ff5252;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #ff6b6b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        .availability-section {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .availability-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .availability-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .availability-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .available { color: #28a745; }
        .unavailable { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 لوحة تحكم تحميل الصوت المحسن</h1>
            <p>مراقبة وإدارة طرق تحميل الصوت المتعددة مع Apify و YouTube-DL</p>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="loading">جاري تحميل الإحصائيات... <span class="spinner"></span></div>
        </div>
        
        <div class="availability-section">
            <h2 class="section-title">🔧 توفر الأدوات</h2>
            <div class="availability-grid" id="availabilityGrid">
                <div class="loading">جاري فحص الأدوات... <span class="spinner"></span></div>
            </div>
        </div>
        
        <div class="methods-section">
            <h2 class="section-title">📊 حالة طرق التحميل</h2>
            <div class="methods-grid" id="methodsGrid">
                <div class="loading">جاري تحميل بيانات طرق التحميل... <span class="spinner"></span></div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshData()" title="تحديث البيانات">
        🔄
    </button>
    
    <script>
        let refreshInterval;
        
        async function loadData() {
            try {
                const response = await fetch('/api/download-status');
                const data = await response.json();
                
                updateStats(data.stats);
                updateAvailability(data.availability);
                updateMethods(data.methods);
                
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
            }
        }
        
        function updateStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <h3>إجمالي طرق التحميل</h3>
                    <div class="stat-value">${stats.total_methods}</div>
                </div>
                <div class="stat-card">
                    <h3>الطرق النشطة</h3>
                    <div class="stat-value">${stats.active_methods}</div>
                </div>
                <div class="stat-card">
                    <h3>رصيد Apify المتبقي</h3>
                    <div class="stat-value">$${stats.apify_remaining || 0}</div>
                </div>
                <div class="stat-card">
                    <h3>إجمالي التحميلات</h3>
                    <div class="stat-value">${stats.total_downloads || 0}</div>
                </div>
            `;
        }
        
        function updateAvailability(availability) {
            const availabilityGrid = document.getElementById('availabilityGrid');
            availabilityGrid.innerHTML = '';
            
            Object.entries(availability).forEach(([tool, available]) => {
                const availabilityCard = document.createElement('div');
                availabilityCard.className = 'availability-card';
                availabilityCard.innerHTML = `
                    <div class="availability-icon ${available ? 'available' : 'unavailable'}">
                        ${available ? '✅' : '❌'}
                    </div>
                    <div>${getToolDisplayName(tool)}</div>
                    <div>${available ? 'متوفر' : 'غير متوفر'}</div>
                `;
                availabilityGrid.appendChild(availabilityCard);
            });
        }
        
        function updateMethods(methods) {
            const methodsGrid = document.getElementById('methodsGrid');
            methodsGrid.innerHTML = '';
            
            Object.entries(methods).forEach(([methodName, method]) => {
                const statusClass = `status-${method.status.replace('_', '-')}`;
                const usagePercent = Math.min(method.usage_percentage, 100);
                
                const methodCard = document.createElement('div');
                methodCard.className = 'method-card';
                methodCard.innerHTML = `
                    <div class="method-header">
                        <div class="method-name">${getMethodDisplayName(methodName)}</div>
                        <div class="method-status ${statusClass}">${getStatusText(method.status)}</div>
                    </div>
                    
                    <div class="method-metrics">
                        <div class="metric">
                            <div class="metric-label">معدل النجاح</div>
                            <div class="metric-value">${method.success_rate}%</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">متوسط الاستجابة</div>
                            <div class="metric-value">${method.avg_response_time}ث</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">الأولوية</div>
                            <div class="metric-value">#${method.priority}</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">نقاط الجودة</div>
                            <div class="metric-value">${method.quality_score}</div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="metric-label">الاستخدام الشهري: ${method.monthly_usage}/${method.monthly_limit}</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${usagePercent}%"></div>
                        </div>
                    </div>
                    
                    <div class="controls">
                        <button class="btn btn-primary" onclick="resetMethod('${methodName}')">إعادة تعيين</button>
                        <button class="btn btn-warning" onclick="testMethod('${methodName}')">اختبار</button>
                    </div>
                `;
                
                methodsGrid.appendChild(methodCard);
            });
        }
        
        function getToolDisplayName(tool) {
            const names = {
                'apify': 'Apify API',
                'youtube_dl': 'YouTube-DL',
                'yt_dlp': 'YT-DLP',
                'ffmpeg': 'FFmpeg'
            };
            return names[tool] || tool;
        }
        
        function getMethodDisplayName(method) {
            const names = {
                'apify': 'Apify YouTube Downloader',
                'youtube_dl': 'YouTube-DL',
                'yt_dlp': 'YT-DLP',
                'pytube': 'PyTube (Legacy)',
                'youtube_api': 'YouTube API (Legacy)'
            };
            return names[method] || method;
        }
        
        function getStatusText(status) {
            const statusMap = {
                'active': 'نشط',
                'error': 'خطأ',
                'rate_limited': 'محدود',
                'quota_exceeded': 'تجاوز الحصة',
                'disabled': 'معطل',
                'unavailable': 'غير متوفر'
            };
            return statusMap[status] || status;
        }
        
        async function resetMethod(methodName) {
            try {
                const response = await fetch(`/api/reset-method/${methodName}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    alert(`تم إعادة تعيين طريقة ${methodName} بنجاح`);
                    loadData();
                } else {
                    alert(`فشل في إعادة تعيين الطريقة: ${result.error}`);
                }
            } catch (error) {
                alert(`خطأ: ${error.message}`);
            }
        }
        
        async function testMethod(methodName) {
            alert(`اختبار طريقة ${methodName} - هذه الميزة قيد التطوير`);
        }
        
        function refreshData() {
            loadData();
        }
        
        // تحميل البيانات عند بدء التشغيل
        loadData();
        
        // تحديث تلقائي كل 30 ثانية
        refreshInterval = setInterval(loadData, 30000);
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """الصفحة الرئيسية للوحة التحكم"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/download-status')
def get_download_status():
    """الحصول على حالة نظام تحميل الصوت"""
    try:
        # الحصول على إحصائيات النظام
        download_stats = enhanced_audio_downloader.get_download_statistics()
        availability = enhanced_audio_downloader.get_method_availability()
        
        # إضافة معلومات Apify
        apify_stats = None
        if apify_downloader.is_available():
            apify_stats = apify_downloader.get_usage_statistics()
            
        return jsonify({
            'success': True,
            'stats': {
                **download_stats,
                'apify_remaining': apify_stats['remaining_credit'] if apify_stats else 0,
                'total_downloads': sum(method.get('total_requests', 0) for method in download_stats.get('methods', {}).values())
            },
            'availability': availability,
            'methods': download_stats.get('methods', {}),
            'apify_details': apify_stats,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على حالة التحميل: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reset-method/<method_name>', methods=['POST'])
def reset_method(method_name):
    """إعادة تعيين طريقة تحميل معينة"""
    try:
        enhanced_audio_downloader.reset_method_health(method_name)
        
        return jsonify({
            'success': True,
            'message': f'تم إعادة تعيين طريقة {method_name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في إعادة تعيين طريقة التحميل {method_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def run_dashboard(host='localhost', port=5002, debug=False):
    """تشغيل لوحة التحكم"""
    logger.info(f"🌐 بدء تشغيل لوحة تحكم تحميل الصوت على {host}:{port}")
    app.run(host=host, port=port, debug=debug, threaded=True)

if __name__ == '__main__':
    print("🎵 لوحة تحكم تحميل الصوت المحسن")
    print("=" * 50)
    print("🌐 الواجهة متاحة على: http://localhost:5002")
    print("🔄 تحديث تلقائي كل 30 ثانية")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        run_dashboard(host='0.0.0.0', port=5002, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف لوحة التحكم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم: {e}")
