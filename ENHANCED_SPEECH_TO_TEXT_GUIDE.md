# 🎤 دليل نظام تحويل النص إلى صوت المحسن

## 📋 نظرة عامة

تم تطوير نظام تحويل النص إلى صوت المحسن ليوفر خدمات متعددة عالية الجودة مع نظام ذكي للأولوية والتبديل التلقائي، مع الاحتفاظ بـ Whisper كبديل احتياطي موثوق.

## ✨ الميزات الرئيسية

### 🎯 خدمات متعددة مع حدود مجانية سخية
- **AssemblyAI**: 416 ساعة مجانية للاختبار
- **Speechmatics**: 480 دقيقة مجانية شهرياً
- **IBM Watson**: 500 دقيقة مجانية شهرياً
- **Microsoft Azure**: 300 دقيقة مجانية شهرياً
- **Google Cloud**: 60 دقيقة مجانية شهرياً
- **Wit.ai**: مجاني بلا حدود
- **Whisper**: البديل الاحتياطي الموثوق

### 🧠 نظام أولوية ذكي
- ترتيب تلقائي للخدمات حسب الجودة والتوفر
- تبديل تلقائي عند فشل إحدى الخدمات
- تتبع الاستخدام والحدود المجانية
- تقييم جودة الخدمات بناءً على الأداء

### 💾 تخزين مؤقت ذكي
- حفظ النتائج لتوفير استهلاك APIs
- ضغط البيانات لتوفير المساحة
- تنظيف تلقائي للمدخلات القديمة
- فهرسة سريعة للبحث

### 🌐 واجهة ويب تفاعلية
- مراقبة حالة الخدمات في الوقت الفعلي
- إحصائيات الاستخدام والأداء
- إدارة وإعادة تعيين الخدمات
- تحديث تلقائي كل 30 ثانية

## 🚀 التثبيت والإعداد

### 1. إعداد مفاتيح APIs

أضف المفاتيح التالية إلى ملف `.env`:

```env
# AssemblyAI
ASSEMBLYAI_API_KEY=your_assemblyai_key

# Speechmatics
SPEECHMATICS_API_KEY=your_speechmatics_key

# IBM Watson
IBM_WATSON_API_KEY=your_watson_key
IBM_WATSON_URL=your_watson_url

# Microsoft Azure
AZURE_SPEECH_KEY=your_azure_key
AZURE_SPEECH_REGION=your_azure_region

# Google Cloud
GOOGLE_CLOUD_SPEECH_KEY=your_google_key
GOOGLE_CLOUD_SPEECH_PROJECT_ID=your_project_id

# Wit.ai
WITAI_ACCESS_TOKEN=your_witai_token

# Whisper (احتياطي)
WHISPER_API_URL=https://nanami34-ai55.hf.space/api/transcribe
WHISPER_API_KEY=whisper-hf-spaces-2025
```

### 2. تثبيت المتطلبات

```bash
pip install aiohttp sqlite3 gzip flask
```

## 📖 كيفية الاستخدام

### الاستخدام الأساسي

```python
from modules.enhanced_speech_integration import enhanced_speech_integration

# تحويل ملف صوتي
async def transcribe_audio():
    with open("audio_file.mp3", "rb") as f:
        audio_data = f.read()
    
    result = await enhanced_speech_integration.transcribe_audio_enhanced(
        audio_data=audio_data,
        video_id="test_123",
        video_title="Test Audio",
        language="auto",  # أو "arabic" أو "english"
        duration_seconds=120.0
    )
    
    if result['success']:
        print(f"النص: {result['text']}")
        print(f"الخدمة المستخدمة: {result['service_used']}")
        print(f"مستوى الثقة: {result['confidence']}")
    else:
        print(f"خطأ: {result['error']}")
```

### التكامل مع محلل YouTube

```python
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer

analyzer = AdvancedYouTubeAnalyzer()

# سيستخدم النظام المحسن تلقائياً
transcript = await analyzer.extract_video_transcript_with_whisper("video_id")
```

### إدارة التخزين المؤقت

```python
from modules.speech_cache_manager import speech_cache_manager

# حفظ نتيجة يدوياً
speech_cache_manager.store_transcription(
    audio_data, text, confidence, language, service_used, duration
)

# البحث في التخزين المؤقت
cached_result = speech_cache_manager.get_transcription(audio_data, language)

# مسح التخزين المؤقت
speech_cache_manager._cleanup_old_entries()
```

### مراقبة النظام

```python
from modules.enhanced_speech_integration import enhanced_speech_integration

# الحصول على حالة النظام
status = enhanced_speech_integration.get_system_status()
print(f"الخدمات النشطة: {status['speech_services']['active_services']}")

# إعادة تعيين خدمة معينة
enhanced_speech_integration.reset_service_health("assemblyai")

# مسح التخزين المؤقت
enhanced_speech_integration.clear_cache()
```

## 🌐 تشغيل واجهة الويب

```bash
python speech_to_text_dashboard.py
```

ثم افتح المتصفح على: `http://localhost:5001`

## 🧪 اختبار النظام

```bash
python test_enhanced_speech_system.py
```

سيقوم الاختبار بفحص:
- تهيئة النظام
- الخدمات الفردية
- نظام الأولوية
- التخزين المؤقت
- التكامل الكامل
- الأداء

## ⚙️ التكوين المتقدم

### تخصيص الأولويات

```python
from modules.speech_priority_manager import speech_priority_manager

# تغيير أولوية خدمة
speech_priority_manager.priority_rules["assemblyai"] = 1  # أعلى أولوية
speech_priority_manager.priority_rules["witai"] = 10      # أقل أولوية
```

### إعدادات التخزين المؤقت

```python
from modules.speech_cache_manager import SpeechCacheManager

# إنشاء مدير تخزين مؤقت مخصص
cache_manager = SpeechCacheManager(
    cache_dir="custom_cache",
    max_size_mb=1000  # 1GB
)
```

## 📊 مراقبة الاستخدام

### إحصائيات الخدمات

```python
from modules.speech_priority_manager import speech_priority_manager

stats = speech_priority_manager.get_service_statistics()
for service, data in stats['services'].items():
    print(f"{service}: {data['usage_percentage']:.1f}% مستخدم")
```

### إحصائيات التخزين المؤقت

```python
from modules.speech_to_text_manager import speech_to_text_manager

usage_stats = speech_to_text_manager.get_usage_statistics()
print(f"حجم التخزين المؤقت: {usage_stats['cache_size']} مدخل")
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **فشل جميع الخدمات**
   ```python
   # فحص مفاتيح APIs
   from config.settings import BotConfig
   print(f"AssemblyAI: {bool(BotConfig.ASSEMBLYAI_API_KEY)}")
   
   # إعادة تعيين صحة الخدمات
   enhanced_speech_integration.reset_service_health()
   ```

2. **بطء في الأداء**
   ```python
   # مسح التخزين المؤقت
   enhanced_speech_integration.clear_cache()
   
   # فحص حجم التخزين المؤقت
   cache_size = speech_cache_manager._get_cache_size()
   print(f"حجم التخزين: {cache_size / 1024 / 1024:.1f}MB")
   ```

3. **تجاوز الحدود المجانية**
   ```python
   # فحص الاستخدام الشهري
   stats = speech_priority_manager.get_service_statistics()
   for service, data in stats['services'].items():
       if data['usage_percentage'] > 90:
           print(f"⚠️ {service} قارب على النفاد: {data['usage_percentage']:.1f}%")
   ```

## 📈 تحسين الأداء

### نصائح للحصول على أفضل أداء

1. **استخدم التخزين المؤقت بذكاء**
   - النظام يحفظ النتائج تلقائياً
   - تجنب معالجة نفس الملف مرتين

2. **اختر اللغة المناسبة**
   ```python
   # بدلاً من "auto"، حدد اللغة إذا كنت تعرفها
   result = await enhanced_speech_integration.transcribe_audio_enhanced(
       audio_data, video_id, title, "arabic", duration  # أو "english"
   )
   ```

3. **راقب استخدام الخدمات**
   - استخدم واجهة الويب لمراقبة الاستخدام
   - وزع الاستخدام على عدة خدمات

## 🔄 التحديثات والصيانة

### تحديث النظام

1. **تحديث قائمة الخدمات**
   ```python
   # إضافة خدمة جديدة
   speech_priority_manager.initialize_service(
       "new_service", monthly_limit=1000
   )
   ```

2. **تنظيف دوري**
   ```python
   # تنظيف التخزين المؤقت شهرياً
   speech_cache_manager._cleanup_expired_entries()
   ```

## 🎯 أفضل الممارسات

1. **استخدم النظام المحسن دائماً**
   ```python
   # بدلاً من استخدام Whisper مباشرة
   result = await enhanced_speech_integration.transcribe_audio_enhanced(...)
   ```

2. **راقب الأداء بانتظام**
   - افتح واجهة الويب يومياً
   - راجع تقارير الاختبار

3. **احتفظ بنسخ احتياطية من المفاتيح**
   - احفظ مفاتيح APIs في مكان آمن
   - استخدم خدمات متعددة للموثوقية

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملفات السجل في `logs/bot.log`
2. شغل الاختبار الشامل لتشخيص المشاكل
3. استخدم واجهة الويب لمراقبة النظام

---

**تم تطوير هذا النظام لتوفير أفضل تجربة لتحويل النص إلى صوت مع موثوقية عالية وتوفير في التكاليف.**
