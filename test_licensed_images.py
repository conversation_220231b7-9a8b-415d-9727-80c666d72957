#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مصادر الصور المرخصة الجديدة
"""

import asyncio
import os
import sys
from datetime import datetime

# إضافة المسار الحالي
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_unsplash():
    """اختبار Unsplash API"""
    print("\n🎨 اختبار Unsplash API...")
    
    try:
        from modules.licensed_image_manager import UnsplashImageProvider
        
        provider = UnsplashImageProvider()
        
        if not provider.enabled:
            print("❌ Unsplash غير مفعل - تحقق من UNSPLASH_ACCESS_KEY")
            return False
        
        # اختبار البحث
        images = await provider.search_game_images("Minecraft", 2)
        
        if images:
            print(f"✅ Unsplash: تم العثور على {len(images)} صورة")
            for i, img in enumerate(images, 1):
                print(f"   {i}. {img.attribution}")
                print(f"      URL: {img.url[:60]}...")
            return True
        else:
            print("⚠️ Unsplash: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Unsplash: {e}")
        return False

async def test_pexels():
    """اختبار Pexels API"""
    print("\n🎨 اختبار Pexels API...")
    
    try:
        from modules.licensed_image_manager import PexelsImageProvider
        
        provider = PexelsImageProvider()
        
        if not provider.enabled:
            print("❌ Pexels غير مفعل - تحقق من PEXELS_API_KEY")
            return False
        
        # اختبار البحث
        images = await provider.search_game_images("gaming", 2)
        
        if images:
            print(f"✅ Pexels: تم العثور على {len(images)} صورة")
            for i, img in enumerate(images, 1):
                print(f"   {i}. {img.attribution}")
                print(f"      URL: {img.url[:60]}...")
            return True
        else:
            print("⚠️ Pexels: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Pexels: {e}")
        return False

async def test_pixabay():
    """اختبار Pixabay API"""
    print("\n🎨 اختبار Pixabay API...")
    
    try:
        from modules.licensed_image_manager import PixabayImageProvider
        
        provider = PixabayImageProvider()
        
        if not provider.enabled:
            print("❌ Pixabay غير مفعل - تحقق من PIXABAY_API_KEY")
            return False
        
        # اختبار البحث
        images = await provider.search_game_images("video games", 2)
        
        if images:
            print(f"✅ Pixabay: تم العثور على {len(images)} صورة")
            for i, img in enumerate(images, 1):
                print(f"   {i}. {img.attribution}")
                print(f"      URL: {img.url[:60]}...")
            return True
        else:
            print("⚠️ Pixabay: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Pixabay: {e}")
        return False

async def test_giant_bomb():
    """اختبار Giant Bomb API"""
    print("\n🎮 اختبار Giant Bomb API...")
    
    try:
        from modules.licensed_image_manager import GiantBombImageProvider
        
        provider = GiantBombImageProvider()
        
        if not provider.enabled:
            print("❌ Giant Bomb غير مفعل - تحقق من GIANT_BOMB_API_KEY")
            return False
        
        # اختبار البحث
        images = await provider.search_game_images("The Legend of Zelda", 2)
        
        if images:
            print(f"✅ Giant Bomb: تم العثور على {len(images)} صورة")
            for i, img in enumerate(images, 1):
                print(f"   {i}. {img.game_name} - {img.image_type}")
                print(f"      URL: {img.url[:60]}...")
            return True
        else:
            print("⚠️ Giant Bomb: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Giant Bomb: {e}")
        return False

async def test_mobygames():
    """اختبار MobyGames API"""
    print("\n🕹️ اختبار MobyGames API...")
    
    try:
        from modules.licensed_image_manager import MobyGamesImageProvider
        
        provider = MobyGamesImageProvider()
        
        if not provider.enabled:
            print("❌ MobyGames غير مفعل - تحقق من MOBYGAMES_API_KEY")
            return False
        
        # اختبار البحث
        images = await provider.search_game_images("Super Mario Bros", 2)
        
        if images:
            print(f"✅ MobyGames: تم العثور على {len(images)} صورة")
            for i, img in enumerate(images, 1):
                print(f"   {i}. {img.image_type}")
                print(f"      URL: {img.url[:60]}...")
            return True
        else:
            print("⚠️ MobyGames: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار MobyGames: {e}")
        return False

async def test_licensed_image_manager():
    """اختبار مدير الصور المرخصة الكامل"""
    print("\n🎯 اختبار مدير الصور المرخصة الكامل...")
    
    try:
        from modules.licensed_image_manager import LicensedImageManager
        
        manager = LicensedImageManager()
        
        # اختبار البحث الشامل
        images = await manager.get_licensed_images_for_game("Cyberpunk 2077", 3)
        
        if images:
            print(f"✅ مدير الصور: تم العثور على {len(images)} صورة")
            
            # عرض إحصائيات المصادر
            sources = {}
            for img in images:
                source = img.source
                sources[source] = sources.get(source, 0) + 1
            
            print("📊 توزيع المصادر:")
            for source, count in sources.items():
                print(f"   • {source}: {count} صورة")
            
            return True
        else:
            print("⚠️ مدير الصور: لم يتم العثور على صور")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الصور: {e}")
        return False

def check_api_keys():
    """فحص توفر مفاتيح API"""
    print("🔑 فحص مفاتيح API...")
    
    keys = {
        'UNSPLASH_ACCESS_KEY': os.getenv('UNSPLASH_ACCESS_KEY'),
        'PEXELS_API_KEY': os.getenv('PEXELS_API_KEY'),
        'PIXABAY_API_KEY': os.getenv('PIXABAY_API_KEY'),
        'GIANT_BOMB_API_KEY': os.getenv('GIANT_BOMB_API_KEY'),
        'MOBYGAMES_API_KEY': os.getenv('MOBYGAMES_API_KEY')
    }
    
    available_keys = 0
    for key_name, key_value in keys.items():
        if key_value:
            print(f"✅ {key_name}: متوفر")
            available_keys += 1
        else:
            print(f"❌ {key_name}: غير متوفر")
    
    print(f"\n📊 المفاتيح المتوفرة: {available_keys}/{len(keys)}")
    return available_keys

async def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار مصادر الصور المرخصة الجديدة")
    print("=" * 50)
    
    # فحص المفاتيح
    available_keys = check_api_keys()
    
    if available_keys == 0:
        print("\n⚠️ لا توجد مفاتيح API متوفرة!")
        print("📋 يرجى إضافة المفاتيح إلى ملف .env")
        return
    
    # اختبار المصادر
    tests = [
        ("Unsplash", test_unsplash),
        ("Pexels", test_pexels),
        ("Pixabay", test_pixabay),
        ("Giant Bomb", test_giant_bomb),
        ("MobyGames", test_mobygames),
        ("مدير الصور الكامل", test_licensed_image_manager)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ فشل اختبار {test_name}: {e}")
            results[test_name] = False
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 ملخص النتائج:")
    
    successful_tests = 0
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   • {test_name}: {status}")
        if result:
            successful_tests += 1
    
    print(f"\n🎯 النتيجة النهائية: {successful_tests}/{len(tests)} اختبار نجح")
    
    if successful_tests > 0:
        print("\n🎉 تم تفعيل مصادر صور جديدة بنجاح!")
        print("💡 يمكنك الآن الاستفادة من المزيد من الصور المرخصة")
    else:
        print("\n⚠️ لم ينجح أي اختبار")
        print("📋 تحقق من مفاتيح API والاتصال بالإنترنت")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
