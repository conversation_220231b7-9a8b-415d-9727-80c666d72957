# 🎵 دليل نظام تحميل الصوت المحسن

## 📋 نظرة عامة

تم تطوير نظام تحميل الصوت المحسن ليحل محل الطرق التقليدية ويوفر خدمات متعددة عالية الجودة مع نظام ذكي للأولوية والتبديل التلقائي. النظام يستخدم Apify و YouTube-DL كطرق أساسية مع الاحتفاظ بالطرق الحالية كبدائل احتياطية.

## ✨ الميزات الرئيسية

### 🎯 طرق تحميل متعددة مع أولوية ذكية
- **🔵 Apify YouTube Downloader**: الأولوية الأولى - $5 شهرياً مجاني (12,500 وحدة)
- **🟠 YouTube-DL**: الأولوية الثانية - مجاني بالكامل وبلا حدود
- **🟠 YT-DLP**: البديل الثالث - نسخة محسنة من YouTube-DL
- **🔄 PyTube**: البديل الرابع - الطريقة الحالية
- **🔄 YouTube API**: البديل الأخير - الطريقة الحالية

### 🧠 نظام أولوية ذكي
- ترتيب تلقائي للطرق حسب الجودة والتوفر والتكلفة
- تبديل تلقائي عند فشل إحدى الطرق
- تتبع دقيق للاستخدام والحدود المجانية
- تقييم مستمر لجودة الطرق بناءً على الأداء الفعلي

### 🔧 دعم أدوات متقدمة
- **Apify API**: خدمة سحابية عالية الجودة
- **YouTube-DL**: أداة سطر أوامر موثوقة
- **YT-DLP**: نسخة محسنة ومحدثة من YouTube-DL
- **FFmpeg**: لتحويل وضغط الصوت

### 🌐 واجهة ويب تفاعلية
- مراقبة حالة الطرق في الوقت الفعلي
- إحصائيات مفصلة للاستخدام والأداء
- إدارة وإعادة تعيين الطرق
- فحص توفر الأدوات

## 🚀 التثبيت والإعداد

### 1. إعداد مفاتيح APIs

أضف المفاتيح التالية إلى ملف `.env`:

```env
# Apify (الأولوية الأولى)
APIFY_API_TOKEN=your_apify_token

# إعدادات YouTube-DL (اختيارية)
YOUTUBE_DL_PATH=youtube-dl  # أو المسار الكامل
FFMPEG_PATH=ffmpeg         # أو المسار الكامل
```

### 2. تثبيت الأدوات المطلوبة

#### تثبيت YouTube-DL:
```bash
# باستخدام pip
pip install youtube-dl

# أو تثبيت YT-DLP (الأفضل)
pip install yt-dlp

# أو تحميل مباشر
curl -L https://yt-dl.org/downloads/latest/youtube-dl -o /usr/local/bin/youtube-dl
chmod a+rx /usr/local/bin/youtube-dl
```

#### تثبيت FFmpeg:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# تحميل من https://ffmpeg.org/download.html
```

### 3. إنشاء حساب Apify (اختياري)

1. اذهب إلى [Apify.com](https://apify.com)
2. أنشئ حساب مجاني
3. احصل على API Token من لوحة التحكم
4. ستحصل على $5 رصيد مجاني شهرياً

## 📖 كيفية الاستخدام

### الاستخدام الأساسي

```python
from modules.enhanced_audio_downloader import enhanced_audio_downloader

# تحميل صوت من فيديو YouTube
async def download_audio():
    result = await enhanced_audio_downloader.download_audio(
        video_id="dQw4w9WgXcQ",
        video_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        quality="medium",  # high, medium, low
        format="mp3"       # mp3, wav, m4a
    )
    
    if result.success:
        print(f"✅ تم التحميل بنجاح!")
        print(f"الطريقة المستخدمة: {result.method_used}")
        print(f"حجم الملف: {result.file_size} بايت")
        print(f"مسار الملف: {result.file_path}")
    else:
        print(f"❌ فشل التحميل: {result.error_message}")
```

### التكامل مع محلل YouTube

```python
from modules.advanced_youtube_analyzer import AdvancedYouTubeAnalyzer

analyzer = AdvancedYouTubeAnalyzer()

# سيستخدم النظام المحسن تلقائياً
transcript = await analyzer.extract_video_transcript_with_whisper("video_id")
```

### استخدام طرق محددة

```python
from modules.apify_downloader import apify_downloader
from modules.youtube_dl_downloader import youtube_dl_downloader

# استخدام Apify مباشرة
apify_result = await apify_downloader.download_audio(
    "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "dQw4w9WgXcQ",
    "medium",
    "mp3"
)

# استخدام YouTube-DL مباشرة
ytdl_result = await youtube_dl_downloader.download_audio(
    "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "dQw4w9WgXcQ", 
    "medium",
    "mp3"
)
```

### مراقبة النظام

```python
from modules.enhanced_audio_downloader import enhanced_audio_downloader

# الحصول على إحصائيات التحميل
stats = enhanced_audio_downloader.get_download_statistics()
print(f"الطرق النشطة: {stats['active_methods']}")

# فحص توفر الأدوات
availability = enhanced_audio_downloader.get_method_availability()
print(f"Apify متوفر: {availability['apify']}")
print(f"YouTube-DL متوفر: {availability['youtube_dl']}")

# إعادة تعيين طريقة معينة
enhanced_audio_downloader.reset_method_health("apify")
```

## 🌐 تشغيل واجهة الويب

```bash
python audio_download_dashboard.py
```

ثم افتح المتصفح على: `http://localhost:5002`

## 🧪 اختبار النظام

```bash
python test_enhanced_audio_download_system.py
```

سيقوم الاختبار بفحص:
- تهيئة النظام
- توفر الأدوات
- الطرق الفردية
- نظام الأولوية
- التكامل الكامل
- الأداء

## ⚙️ التكوين المتقدم

### تخصيص الأولويات

```python
from modules.audio_download_priority_manager import audio_download_priority_manager

# تغيير أولوية طريقة
audio_download_priority_manager.priority_rules["apify"] = 1      # أعلى أولوية
audio_download_priority_manager.priority_rules["youtube_dl"] = 2  # الثانية
```

### إعدادات جودة الصوت

```python
# في config/settings.py
AUDIO_QUALITY_SETTINGS = {
    "high": {
        "format": "bestaudio/best",
        "audio_format": "mp3",
        "audio_quality": "320k"  # جودة عالية
    },
    "medium": {
        "format": "bestaudio[abr<=128]/best[abr<=128]",
        "audio_format": "mp3", 
        "audio_quality": "128k"  # جودة متوسطة
    },
    "low": {
        "format": "bestaudio[abr<=64]/best[abr<=64]",
        "audio_format": "mp3",
        "audio_quality": "64k"   # جودة منخفضة
    }
}
```

## 📊 مراقبة الاستخدام

### إحصائيات Apify

```python
from modules.apify_downloader import apify_downloader

stats = apify_downloader.get_usage_statistics()
print(f"الرصيد المتبقي: ${stats['remaining_credit']:.2f}")
print(f"الاستخدام الشهري: ${stats['monthly_usage']:.2f}")
print(f"معدل النجاح: {stats['success_rate']:.1f}%")
```

### إحصائيات YouTube-DL

```python
from modules.youtube_dl_downloader import youtube_dl_downloader

info = youtube_dl_downloader.get_tool_info()
print(f"YouTube-DL متوفر: {info['youtube_dl_available']}")
print(f"YT-DLP متوفر: {info['yt_dlp_available']}")
print(f"FFmpeg متوفر: {info['ffmpeg_available']}")
print(f"الأداة المفضلة: {info['preferred_tool']}")
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **فشل جميع طرق التحميل**
   ```python
   # فحص توفر الأدوات
   availability = enhanced_audio_downloader.get_method_availability()
   print(availability)
   
   # إعادة تعيين صحة الطرق
   enhanced_audio_downloader.reset_method_health()
   ```

2. **مشاكل Apify**
   ```python
   # فحص الرصيد
   if apify_downloader.is_available():
       credit = apify_downloader.get_remaining_credit()
       print(f"الرصيد المتبقي: ${credit:.2f}")
   else:
       print("Apify غير متوفر - تحقق من API Token")
   ```

3. **مشاكل YouTube-DL**
   ```bash
   # تحديث YouTube-DL
   pip install --upgrade youtube-dl
   
   # أو استخدام YT-DLP
   pip install --upgrade yt-dlp
   
   # فحص FFmpeg
   ffmpeg -version
   ```

4. **بطء في التحميل**
   ```python
   # استخدام جودة أقل
   result = await enhanced_audio_downloader.download_audio(
       video_id, video_url, quality="low", format="mp3"
   )
   ```

## 📈 تحسين الأداء

### نصائح للحصول على أفضل أداء

1. **استخدم الجودة المناسبة**
   - `high`: للجودة العالية (320k)
   - `medium`: للاستخدام العام (128k)
   - `low`: للسرعة (64k)

2. **راقب استخدام Apify**
   - استخدم واجهة الويب لمراقبة الرصيد
   - وزع الاستخدام على الشهر

3. **تأكد من تحديث الأدوات**
   ```bash
   pip install --upgrade yt-dlp youtube-dl
   ```

## 🔄 التحديثات والصيانة

### تحديث النظام

1. **إضافة طريقة تحميل جديدة**
   ```python
   # في enhanced_audio_downloader.py
   audio_download_priority_manager.initialize_method(
       "new_method", monthly_limit=1000, cost_per_use=0.001
   )
   ```

2. **تحديث أولويات الطرق**
   ```python
   # تعديل الأولويات حسب الأداء
   priority_manager.priority_rules["apify"] = 1
   priority_manager.priority_rules["youtube_dl"] = 2
   ```

## 🎯 أفضل الممارسات

1. **استخدم النظام المحسن دائماً**
   ```python
   # بدلاً من استخدام طريقة واحدة
   result = await enhanced_audio_downloader.download_audio(...)
   ```

2. **راقب الأداء بانتظام**
   - افتح واجهة الويب يومياً
   - راجع تقارير الاختبار

3. **احتفظ بنسخ احتياطية من المفاتيح**
   - احفظ Apify API Token في مكان آمن
   - استخدم طرق متعددة للموثوقية

## 📞 الدعم والمساعدة

للحصول على المساعدة:
1. راجع ملفات السجل في `logs/bot.log`
2. شغل الاختبار الشامل لتشخيص المشاكل
3. استخدم واجهة الويب لمراقبة النظام

---

**تم تطوير هذا النظام لتوفير أفضل تجربة لتحميل الصوت مع موثوقية عالية وتوفير في التكاليف.**
