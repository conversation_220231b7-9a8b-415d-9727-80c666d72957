# اختبار شامل لنظام تحميل الصوت المحسن
import asyncio
import os
import json
import time
from datetime import datetime
from typing import Dict, Any

from modules.enhanced_audio_downloader import enhanced_audio_downloader
from modules.apify_downloader import apify_downloader
from modules.youtube_dl_downloader import youtube_dl_downloader
from modules.audio_download_priority_manager import audio_download_priority_manager
from modules.logger import logger

class EnhancedAudioDownloadSystemTester:
    """اختبار شامل لنظام تحميل الصوت المحسن"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
        # فيديوهات اختبار قصيرة
        self.test_videos = [
            {
                "id": "dQw4w9WgXcQ",  # Rick Roll - فيديو قصير مشهور
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "title": "<PERSON> Astley - Never Gonna Give You Up"
            },
            {
                "id": "9bZkp7q19f0",  # PSY - GANGNAM STYLE
                "url": "https://www.youtube.com/watch?v=9bZkp7q19f0", 
                "title": "PSY - GANGNAM STYLE"
            }
        ]
        
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("🎵 بدء اختبار نظام تحميل الصوت المحسن")
        print("=" * 60)
        
        try:
            # 1. اختبار تهيئة النظام
            await self._test_system_initialization()
            
            # 2. اختبار توفر الأدوات
            await self._test_tools_availability()
            
            # 3. اختبار طرق التحميل الفردية
            await self._test_individual_download_methods()
            
            # 4. اختبار نظام الأولوية
            await self._test_priority_system()
            
            # 5. اختبار التكامل الكامل
            await self._test_full_integration()
            
            # 6. اختبار الأداء
            await self._test_performance()
            
            # 7. إنشاء التقرير
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
            print(f"❌ فشل الاختبار: {e}")
            
    async def _test_system_initialization(self):
        """اختبار تهيئة النظام"""
        print("\n🔧 اختبار تهيئة النظام...")
        
        try:
            # فحص تهيئة المكونات
            assert enhanced_audio_downloader is not None, "فشل تهيئة مدير التحميل المحسن"
            assert apify_downloader is not None, "فشل تهيئة Apify downloader"
            assert youtube_dl_downloader is not None, "فشل تهيئة YouTube-DL downloader"
            assert audio_download_priority_manager is not None, "فشل تهيئة مدير الأولوية"
            
            # فحص الطرق المتاحة
            methods = enhanced_audio_downloader.download_methods
            print(f"✅ تم تهيئة {len(methods)} طريقة تحميل")
            
            for method, config in methods.items():
                status = "✅ مفعل" if config.enabled else "❌ معطل"
                print(f"   - {config.name}: {status} (أولوية: {config.priority})")
                
            self.test_results['initialization'] = {
                'status': 'success',
                'methods_count': len(methods),
                'enabled_methods': sum(1 for config in methods.values() if config.enabled)
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار التهيئة: {e}")
            self.test_results['initialization'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_tools_availability(self):
        """اختبار توفر الأدوات"""
        print("\n🔍 اختبار توفر الأدوات...")
        
        try:
            availability = enhanced_audio_downloader.get_method_availability()
            
            tools_status = {}
            for tool, available in availability.items():
                status = "✅ متوفر" if available else "❌ غير متوفر"
                print(f"   - {tool}: {status}")
                tools_status[tool] = available
                
            # اختبار Apify
            apify_available = apify_downloader.is_available()
            apify_credit = apify_downloader.get_remaining_credit()
            print(f"   - Apify: {'✅ متوفر' if apify_available else '❌ غير متوفر'} (رصيد: ${apify_credit:.2f})")
            
            self.test_results['tools_availability'] = {
                'status': 'success',
                'tools': tools_status,
                'apify_available': apify_available,
                'apify_credit': apify_credit
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار توفر الأدوات: {e}")
            self.test_results['tools_availability'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_individual_download_methods(self):
        """اختبار طرق التحميل الفردية"""
        print("\n🔍 اختبار طرق التحميل الفردية...")
        
        test_video = self.test_videos[0]  # استخدام أول فيديو للاختبار
        method_results = {}
        
        # اختبار Apify
        if apify_downloader.is_available():
            print(f"🔄 اختبار Apify...")
            try:
                start_time = time.time()
                result = await apify_downloader.download_audio(
                    test_video["url"], test_video["id"], "low", "mp3"
                )
                processing_time = time.time() - start_time
                
                if result.success:
                    print(f"✅ Apify: نجح ({processing_time:.1f}ث)")
                    method_results['apify'] = {
                        'status': 'success',
                        'processing_time': processing_time,
                        'file_size': result.file_size,
                        'duration': result.duration_seconds
                    }
                else:
                    print(f"❌ Apify: فشل - {result.error_message}")
                    method_results['apify'] = {
                        'status': 'failed',
                        'error': result.error_message
                    }
                    
            except Exception as e:
                print(f"❌ Apify: خطأ - {e}")
                method_results['apify'] = {
                    'status': 'error',
                    'error': str(e)
                }
        else:
            print("⏭️ تخطي Apify - غير متوفر")
            method_results['apify'] = {'status': 'skipped', 'reason': 'not_available'}
            
        # اختبار YouTube-DL
        if youtube_dl_downloader.is_available():
            print(f"🔄 اختبار YouTube-DL...")
            try:
                start_time = time.time()
                result = await youtube_dl_downloader.download_audio(
                    test_video["url"], test_video["id"], "low", "mp3"
                )
                processing_time = time.time() - start_time
                
                if result.success:
                    print(f"✅ YouTube-DL: نجح ({processing_time:.1f}ث)")
                    method_results['youtube_dl'] = {
                        'status': 'success',
                        'processing_time': processing_time,
                        'file_size': result.file_size,
                        'tool_used': result.tool_used
                    }
                else:
                    print(f"❌ YouTube-DL: فشل - {result.error_message}")
                    method_results['youtube_dl'] = {
                        'status': 'failed',
                        'error': result.error_message
                    }
                    
            except Exception as e:
                print(f"❌ YouTube-DL: خطأ - {e}")
                method_results['youtube_dl'] = {
                    'status': 'error',
                    'error': str(e)
                }
        else:
            print("⏭️ تخطي YouTube-DL - غير متوفر")
            method_results['youtube_dl'] = {'status': 'skipped', 'reason': 'not_available'}
            
        self.test_results['individual_methods'] = method_results
        
    async def _test_priority_system(self):
        """اختبار نظام الأولوية"""
        print("\n🎯 اختبار نظام الأولوية...")
        
        try:
            # الحصول على أفضل طريقة
            best_method = audio_download_priority_manager.get_best_method("medium")
            print(f"🏆 أفضل طريقة: {best_method}")
            
            # الحصول على إحصائيات الطرق
            stats = audio_download_priority_manager.get_download_statistics()
            print(f"📊 إجمالي الطرق: {stats['total_methods']}")
            print(f"📊 الطرق النشطة: {stats['active_methods']}")
            
            # عرض تفاصيل كل طريقة
            for method_name, method_stats in stats['methods'].items():
                print(f"   - {method_name}: أولوية {method_stats['priority']}, جودة {method_stats['quality_score']}")
                
            self.test_results['priority_system'] = {
                'status': 'success',
                'best_method': best_method,
                'total_methods': stats['total_methods'],
                'active_methods': stats['active_methods']
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار نظام الأولوية: {e}")
            self.test_results['priority_system'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_full_integration(self):
        """اختبار التكامل الكامل"""
        print("\n🚀 اختبار التكامل الكامل...")
        
        try:
            test_video = self.test_videos[0]
            
            # اختبار النظام المحسن
            start_time = time.time()
            result = await enhanced_audio_downloader.download_audio(
                test_video["id"], test_video["url"], "medium", "mp3"
            )
            processing_time = time.time() - start_time
            
            if result.success:
                print(f"✅ التكامل الكامل نجح في {processing_time:.1f}ث")
                print(f"📝 الطريقة المستخدمة: {result.method_used}")
                print(f"📁 حجم الملف: {result.file_size} بايت")
                print(f"⏱️ مدة الصوت: {result.duration_seconds:.1f}ث")
                
                self.test_results['full_integration'] = {
                    'status': 'success',
                    'processing_time': processing_time,
                    'method_used': result.method_used,
                    'file_size': result.file_size,
                    'duration': result.duration_seconds
                }
            else:
                print(f"❌ فشل التكامل الكامل: {result.error_message}")
                self.test_results['full_integration'] = {
                    'status': 'failed',
                    'error': result.error_message
                }
                
        except Exception as e:
            print(f"❌ خطأ في اختبار التكامل الكامل: {e}")
            self.test_results['full_integration'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        try:
            test_video = self.test_videos[0]
            test_count = 3
            total_time = 0
            successful_tests = 0
            
            for i in range(test_count):
                print(f"🔄 اختبار الأداء {i+1}/{test_count}...")
                
                start_time = time.time()
                result = await enhanced_audio_downloader.download_audio(
                    test_video["id"], test_video["url"], "low", "mp3"
                )
                processing_time = time.time() - start_time
                
                total_time += processing_time
                
                if result.success:
                    successful_tests += 1
                    print(f"✅ اختبار {i+1}: {processing_time:.1f}ث ({result.method_used})")
                else:
                    print(f"❌ اختبار {i+1}: فشل")
                    
            avg_time = total_time / test_count
            success_rate = (successful_tests / test_count) * 100
            
            print(f"📊 متوسط وقت المعالجة: {avg_time:.1f}ث")
            print(f"📊 معدل النجاح: {success_rate:.1f}%")
            
            self.test_results['performance'] = {
                'status': 'success',
                'test_count': test_count,
                'successful_tests': successful_tests,
                'avg_processing_time': avg_time,
                'success_rate': success_rate
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار الأداء: {e}")
            self.test_results['performance'] = {'status': 'failed', 'error': str(e)}
            
    def _generate_test_report(self):
        """إنشاء تقرير الاختبار"""
        print("\n📋 تقرير الاختبار الشامل")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') == 'success')
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ الاختبارات الناجحة: {successful_tests}")
        print(f"❌ الاختبارات الفاشلة: {total_tests - successful_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        
        # تفاصيل كل اختبار
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            print(f"\n{status_icon} {test_name}:")
            
            if result.get('status') == 'success':
                for key, value in result.items():
                    if key != 'status':
                        print(f"   - {key}: {value}")
            else:
                print(f"   - خطأ: {result.get('error', 'غير محدد')}")
                
        # حفظ التقرير
        report_file = f"audio_download_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_results': self.test_results,
                    'summary': {
                        'total_tests': total_tests,
                        'successful_tests': successful_tests,
                        'success_rate': (successful_tests/total_tests)*100,
                        'test_duration': (datetime.now() - self.start_time).total_seconds()
                    }
                }, f, ensure_ascii=False, indent=2, default=str)
                
            print(f"\n💾 تم حفظ التقرير في: {report_file}")
            
        except Exception as e:
            print(f"⚠️ فشل في حفظ التقرير: {e}")
            
        print("\n🎉 اكتمل الاختبار الشامل!")

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = EnhancedAudioDownloadSystemTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
