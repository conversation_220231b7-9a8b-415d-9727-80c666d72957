# واجهة ويب لإدارة خدمات تحويل النص إلى صوت
from flask import Flask, render_template_string, jsonify, request
import json
import os
from datetime import datetime
import threading
import time

from modules.enhanced_speech_integration import enhanced_speech_integration
from modules.speech_priority_manager import speech_priority_manager
from modules.speech_to_text_manager import speech_to_text_manager
from modules.logger import logger

app = Flask(__name__)

# HTML Template للواجهة
HTML_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم خدمات تحويل النص إلى صوت</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 10px;
        }
        
        .services-section {
            padding: 30px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .service-card {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .service-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        
        .service-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-rate-limited { background: #fff3cd; color: #856404; }
        .status-quota-exceeded { background: #f5c6cb; color: #721c24; }
        .status-disabled { background: #e2e3e5; color: #383d41; }
        
        .service-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transition: width 0.3s ease;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #4facfe;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3d8bfe;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            background: #3d8bfe;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2em;
            color: #666;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 لوحة تحكم خدمات تحويل النص إلى صوت</h1>
            <p>مراقبة وإدارة خدمات تحويل النص إلى صوت المتعددة</p>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="loading">جاري تحميل الإحصائيات... <span class="spinner"></span></div>
        </div>
        
        <div class="services-section">
            <h2 class="section-title">📊 حالة الخدمات</h2>
            <div class="services-grid" id="servicesGrid">
                <div class="loading">جاري تحميل بيانات الخدمات... <span class="spinner"></span></div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshData()" title="تحديث البيانات">
        🔄
    </button>
    
    <script>
        let refreshInterval;
        
        async function loadData() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                updateStats(data.stats);
                updateServices(data.services);
                
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
            }
        }
        
        function updateStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <h3>إجمالي الخدمات</h3>
                    <div class="stat-value">${stats.total_services}</div>
                </div>
                <div class="stat-card">
                    <h3>الخدمات النشطة</h3>
                    <div class="stat-value">${stats.active_services}</div>
                </div>
                <div class="stat-card">
                    <h3>حجم التخزين المؤقت</h3>
                    <div class="stat-value">${stats.cache_size}</div>
                </div>
                <div class="stat-card">
                    <h3>الاستخدام الشهري</h3>
                    <div class="stat-value">${stats.total_monthly_usage} دقيقة</div>
                </div>
            `;
        }
        
        function updateServices(services) {
            const servicesGrid = document.getElementById('servicesGrid');
            servicesGrid.innerHTML = '';
            
            Object.entries(services).forEach(([serviceName, service]) => {
                const statusClass = `status-${service.status.replace('_', '-')}`;
                const usagePercent = Math.min(service.usage_percentage, 100);
                
                const serviceCard = document.createElement('div');
                serviceCard.className = 'service-card';
                serviceCard.innerHTML = `
                    <div class="service-header">
                        <div class="service-name">${service.name}</div>
                        <div class="service-status ${statusClass}">${getStatusText(service.status)}</div>
                    </div>
                    
                    <div class="service-metrics">
                        <div class="metric">
                            <div class="metric-label">معدل النجاح</div>
                            <div class="metric-value">${service.success_rate}%</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">متوسط الاستجابة</div>
                            <div class="metric-value">${service.avg_response_time}ث</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">الأولوية</div>
                            <div class="metric-value">#${service.priority}</div>
                        </div>
                        <div class="metric">
                            <div class="metric-label">نقاط الجودة</div>
                            <div class="metric-value">${service.quality_score}</div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="metric-label">الاستخدام الشهري: ${service.monthly_usage}/${service.monthly_limit} دقيقة</div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${usagePercent}%"></div>
                        </div>
                    </div>
                    
                    <div class="controls">
                        <button class="btn btn-primary" onclick="resetService('${serviceName}')">إعادة تعيين</button>
                        <button class="btn btn-warning" onclick="testService('${serviceName}')">اختبار</button>
                    </div>
                `;
                
                servicesGrid.appendChild(serviceCard);
            });
        }
        
        function getStatusText(status) {
            const statusMap = {
                'active': 'نشط',
                'error': 'خطأ',
                'rate_limited': 'محدود',
                'quota_exceeded': 'تجاوز الحصة',
                'disabled': 'معطل'
            };
            return statusMap[status] || status;
        }
        
        async function resetService(serviceName) {
            try {
                const response = await fetch(`/api/reset/${serviceName}`, { method: 'POST' });
                const result = await response.json();
                
                if (result.success) {
                    alert(`تم إعادة تعيين خدمة ${serviceName} بنجاح`);
                    loadData();
                } else {
                    alert(`فشل في إعادة تعيين الخدمة: ${result.error}`);
                }
            } catch (error) {
                alert(`خطأ: ${error.message}`);
            }
        }
        
        async function testService(serviceName) {
            alert(`اختبار خدمة ${serviceName} - هذه الميزة قيد التطوير`);
        }
        
        function refreshData() {
            loadData();
        }
        
        // تحميل البيانات عند بدء التشغيل
        loadData();
        
        // تحديث تلقائي كل 30 ثانية
        refreshInterval = setInterval(loadData, 30000);
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """الصفحة الرئيسية للوحة التحكم"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/status')
def get_status():
    """الحصول على حالة جميع الخدمات"""
    try:
        # الحصول على إحصائيات النظام
        system_status = enhanced_speech_integration.get_system_status()
        
        return jsonify({
            'success': True,
            'stats': system_status['usage_stats'],
            'services': system_status['speech_services']['services'],
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في الحصول على حالة النظام: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/reset/<service_name>', methods=['POST'])
def reset_service(service_name):
    """إعادة تعيين خدمة معينة"""
    try:
        enhanced_speech_integration.reset_service_health(service_name)
        
        return jsonify({
            'success': True,
            'message': f'تم إعادة تعيين خدمة {service_name} بنجاح'
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في إعادة تعيين الخدمة {service_name}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/clear-cache', methods=['POST'])
def clear_cache():
    """مسح التخزين المؤقت"""
    try:
        enhanced_speech_integration.clear_cache()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح التخزين المؤقت بنجاح'
        })
        
    except Exception as e:
        logger.error(f"❌ خطأ في مسح التخزين المؤقت: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def run_dashboard(host='localhost', port=5001, debug=False):
    """تشغيل لوحة التحكم"""
    logger.info(f"🌐 بدء تشغيل لوحة تحكم خدمات تحويل النص إلى صوت على {host}:{port}")
    app.run(host=host, port=port, debug=debug, threaded=True)

if __name__ == '__main__':
    print("🎤 لوحة تحكم خدمات تحويل النص إلى صوت")
    print("=" * 50)
    print("🌐 الواجهة متاحة على: http://localhost:5001")
    print("🔄 تحديث تلقائي كل 30 ثانية")
    print("⏹️ اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        run_dashboard(host='0.0.0.0', port=5001, debug=False)
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف لوحة التحكم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل لوحة التحكم: {e}")
