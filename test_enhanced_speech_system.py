# اختبار شامل لنظام تحويل النص إلى صوت المحسن
import asyncio
import os
import json
import time
from datetime import datetime
from typing import Dict, Any

from modules.enhanced_speech_integration import enhanced_speech_integration
from modules.speech_to_text_manager import speech_to_text_manager
from modules.speech_priority_manager import speech_priority_manager
from modules.speech_cache_manager import speech_cache_manager
from modules.logger import logger

class EnhancedSpeechSystemTester:
    """اختبار شامل لنظام تحويل النص إلى صوت المحسن"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل للنظام"""
        print("🎤 بدء اختبار نظام تحويل النص إلى صوت المحسن")
        print("=" * 60)
        
        try:
            # 1. اختبار تهيئة النظام
            await self._test_system_initialization()
            
            # 2. اختبار الخدمات الفردية
            await self._test_individual_services()
            
            # 3. اختبار نظام الأولوية
            await self._test_priority_system()
            
            # 4. اختبار التخزين المؤقت
            await self._test_caching_system()
            
            # 5. اختبار التكامل الكامل
            await self._test_full_integration()
            
            # 6. اختبار الأداء
            await self._test_performance()
            
            # 7. إنشاء التقرير
            self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار الشامل: {e}")
            print(f"❌ فشل الاختبار: {e}")
            
    async def _test_system_initialization(self):
        """اختبار تهيئة النظام"""
        print("\n🔧 اختبار تهيئة النظام...")
        
        try:
            # فحص تهيئة المكونات
            assert enhanced_speech_integration is not None, "فشل تهيئة التكامل المحسن"
            assert speech_to_text_manager is not None, "فشل تهيئة مدير تحويل النص"
            assert speech_priority_manager is not None, "فشل تهيئة مدير الأولوية"
            assert speech_cache_manager is not None, "فشل تهيئة مدير التخزين المؤقت"
            
            # فحص الخدمات المتاحة
            services = speech_to_text_manager.services
            print(f"✅ تم تهيئة {len(services)} خدمة")
            
            for service, config in services.items():
                status = "✅ مفعل" if config.enabled else "❌ معطل"
                print(f"   - {config.name}: {status}")
                
            self.test_results['initialization'] = {
                'status': 'success',
                'services_count': len(services),
                'enabled_services': sum(1 for config in services.values() if config.enabled)
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار التهيئة: {e}")
            self.test_results['initialization'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_individual_services(self):
        """اختبار الخدمات الفردية"""
        print("\n🔍 اختبار الخدمات الفردية...")
        
        # إنشاء ملف صوتي تجريبي صغير
        test_audio = self._create_test_audio()
        
        service_results = {}
        
        for service, config in speech_to_text_manager.services.items():
            if not config.enabled:
                continue
                
            print(f"🔄 اختبار {config.name}...")
            
            try:
                start_time = time.time()
                
                # محاولة التحويل
                result = await speech_to_text_manager._transcribe_with_service(
                    service, config, test_audio, "en", 5.0
                )
                
                processing_time = time.time() - start_time
                
                if result.success:
                    print(f"✅ {config.name}: نجح ({processing_time:.1f}ث)")
                    service_results[service.value] = {
                        'status': 'success',
                        'processing_time': processing_time,
                        'text_length': len(result.text),
                        'confidence': result.confidence
                    }
                else:
                    print(f"❌ {config.name}: فشل - {result.error_message}")
                    service_results[service.value] = {
                        'status': 'failed',
                        'error': result.error_message
                    }
                    
            except Exception as e:
                print(f"❌ {config.name}: خطأ - {e}")
                service_results[service.value] = {
                    'status': 'error',
                    'error': str(e)
                }
                
        self.test_results['individual_services'] = service_results
        
    async def _test_priority_system(self):
        """اختبار نظام الأولوية"""
        print("\n🎯 اختبار نظام الأولوية...")
        
        try:
            # الحصول على أفضل خدمة
            best_service = speech_priority_manager.get_best_service(1.0)
            print(f"🏆 أفضل خدمة: {best_service}")
            
            # الحصول على إحصائيات الخدمات
            stats = speech_priority_manager.get_service_statistics()
            print(f"📊 إجمالي الخدمات: {stats['total_services']}")
            print(f"📊 الخدمات النشطة: {stats['active_services']}")
            
            # عرض تفاصيل كل خدمة
            for service_name, service_stats in stats['services'].items():
                print(f"   - {service_name}: أولوية {service_stats['priority']}, جودة {service_stats['quality_score']}")
                
            self.test_results['priority_system'] = {
                'status': 'success',
                'best_service': best_service,
                'total_services': stats['total_services'],
                'active_services': stats['active_services']
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار نظام الأولوية: {e}")
            self.test_results['priority_system'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_caching_system(self):
        """اختبار نظام التخزين المؤقت"""
        print("\n💾 اختبار نظام التخزين المؤقت...")
        
        try:
            test_audio = self._create_test_audio()
            test_text = "This is a test transcription for caching system."
            
            # حفظ في التخزين المؤقت
            saved = speech_cache_manager.store_transcription(
                test_audio, test_text, 0.95, "en", "test_service", 5.0
            )
            
            if saved:
                print("✅ تم حفظ النص في التخزين المؤقت")
                
                # البحث في التخزين المؤقت
                cached_result = speech_cache_manager.get_transcription(test_audio, "en")
                
                if cached_result and cached_result['text'] == test_text:
                    print("✅ تم استرجاع النص من التخزين المؤقت بنجاح")
                    self.test_results['caching_system'] = {
                        'status': 'success',
                        'save_test': True,
                        'retrieve_test': True
                    }
                else:
                    print("❌ فشل في استرجاع النص من التخزين المؤقت")
                    self.test_results['caching_system'] = {
                        'status': 'partial',
                        'save_test': True,
                        'retrieve_test': False
                    }
            else:
                print("❌ فشل في حفظ النص في التخزين المؤقت")
                self.test_results['caching_system'] = {
                    'status': 'failed',
                    'save_test': False,
                    'retrieve_test': False
                }
                
        except Exception as e:
            print(f"❌ فشل اختبار التخزين المؤقت: {e}")
            self.test_results['caching_system'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_full_integration(self):
        """اختبار التكامل الكامل"""
        print("\n🚀 اختبار التكامل الكامل...")
        
        try:
            test_audio = self._create_test_audio()
            
            # اختبار النظام المحسن
            start_time = time.time()
            result = await enhanced_speech_integration.transcribe_audio_enhanced(
                test_audio, "test_video", "Test Video", "en", 5.0
            )
            processing_time = time.time() - start_time
            
            if result['success']:
                print(f"✅ التكامل الكامل نجح في {processing_time:.1f}ث")
                print(f"📝 النص: {result['text'][:100]}...")
                print(f"🎯 الخدمة المستخدمة: {result['service_used']}")
                print(f"🎯 مستوى الثقة: {result['confidence']:.2f}")
                
                self.test_results['full_integration'] = {
                    'status': 'success',
                    'processing_time': processing_time,
                    'service_used': result['service_used'],
                    'confidence': result['confidence'],
                    'text_length': len(result['text'])
                }
            else:
                print(f"❌ فشل التكامل الكامل: {result.get('error', 'خطأ غير محدد')}")
                self.test_results['full_integration'] = {
                    'status': 'failed',
                    'error': result.get('error', 'خطأ غير محدد')
                }
                
        except Exception as e:
            print(f"❌ خطأ في اختبار التكامل الكامل: {e}")
            self.test_results['full_integration'] = {'status': 'failed', 'error': str(e)}
            
    async def _test_performance(self):
        """اختبار الأداء"""
        print("\n⚡ اختبار الأداء...")
        
        try:
            test_audio = self._create_test_audio()
            test_count = 3
            total_time = 0
            successful_tests = 0
            
            for i in range(test_count):
                print(f"🔄 اختبار الأداء {i+1}/{test_count}...")
                
                start_time = time.time()
                result = await enhanced_speech_integration.transcribe_audio_enhanced(
                    test_audio, f"test_video_{i}", "Performance Test", "en", 5.0
                )
                processing_time = time.time() - start_time
                
                total_time += processing_time
                
                if result['success']:
                    successful_tests += 1
                    print(f"✅ اختبار {i+1}: {processing_time:.1f}ث")
                else:
                    print(f"❌ اختبار {i+1}: فشل")
                    
            avg_time = total_time / test_count
            success_rate = (successful_tests / test_count) * 100
            
            print(f"📊 متوسط وقت المعالجة: {avg_time:.1f}ث")
            print(f"📊 معدل النجاح: {success_rate:.1f}%")
            
            self.test_results['performance'] = {
                'status': 'success',
                'test_count': test_count,
                'successful_tests': successful_tests,
                'avg_processing_time': avg_time,
                'success_rate': success_rate
            }
            
        except Exception as e:
            print(f"❌ فشل اختبار الأداء: {e}")
            self.test_results['performance'] = {'status': 'failed', 'error': str(e)}
            
    def _create_test_audio(self) -> bytes:
        """إنشاء ملف صوتي تجريبي"""
        # إنشاء بيانات صوتية وهمية (في التطبيق الحقيقي، استخدم ملف صوتي حقيقي)
        return b"fake_audio_data_for_testing" * 1000
        
    def _generate_test_report(self):
        """إنشاء تقرير الاختبار"""
        print("\n📋 تقرير الاختبار الشامل")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() 
                             if result.get('status') == 'success')
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ الاختبارات الناجحة: {successful_tests}")
        print(f"❌ الاختبارات الفاشلة: {total_tests - successful_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        
        # تفاصيل كل اختبار
        for test_name, result in self.test_results.items():
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            print(f"\n{status_icon} {test_name}:")
            
            if result.get('status') == 'success':
                for key, value in result.items():
                    if key != 'status':
                        print(f"   - {key}: {value}")
            else:
                print(f"   - خطأ: {result.get('error', 'غير محدد')}")
                
        # حفظ التقرير
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'test_results': self.test_results,
                    'summary': {
                        'total_tests': total_tests,
                        'successful_tests': successful_tests,
                        'success_rate': (successful_tests/total_tests)*100,
                        'test_duration': (datetime.now() - self.start_time).total_seconds()
                    }
                }, f, ensure_ascii=False, indent=2, default=str)
                
            print(f"\n💾 تم حفظ التقرير في: {report_file}")
            
        except Exception as e:
            print(f"⚠️ فشل في حفظ التقرير: {e}")
            
        print("\n🎉 اكتمل الاختبار الشامل!")

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = EnhancedSpeechSystemTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
