# مدير تحميل الصوت المحسن - Enhanced Audio Download Manager
import asyncio
import aiohttp
import os
import json
import time
import subprocess
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import hashlib

from modules.logger import logger
from config.settings import BotConfig
from modules.apify_downloader import apify_downloader
from modules.youtube_dl_downloader import youtube_dl_downloader
from modules.audio_download_priority_manager import audio_download_priority_manager

class AudioDownloadMethod(Enum):
    """طرق تحميل الصوت"""
    APIFY = "apify"
    YOUTUBE_DL = "youtube_dl"
    YT_DLP = "yt_dlp"
    PYTUBE = "pytube"
    YOUTUBE_API = "youtube_api"

@dataclass
class DownloadResult:
    """نتيجة تحميل الصوت"""
    success: bool
    audio_data: Optional[bytes]
    file_path: Optional[str]
    method_used: str
    processing_time: float
    file_size: int
    audio_format: str
    quality: str
    error_message: Optional[str] = None
    duration_seconds: float = 0.0

@dataclass
class DownloadMethodConfig:
    """تكوين طريقة التحميل"""
    name: str
    enabled: bool
    priority: int
    cost_per_use: float  # التكلفة لكل استخدام
    monthly_limit: int   # الحد الشهري
    timeout_seconds: int
    quality_score: float  # نقاط الجودة (0-1)
    supports_formats: List[str]

class EnhancedAudioDownloader:
    """مدير تحميل الصوت المحسن مع طرق متعددة"""
    
    def __init__(self):
        self.download_methods = self._initialize_download_methods()
        self.usage_stats = self._load_usage_stats()
        self.temp_dir = "temp/audio_downloads"
        self.cache_dir = "cache/audio_downloads"
        self.priority_manager = audio_download_priority_manager

        # إنشاء المجلدات المطلوبة
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(BotConfig.YOUTUBE_DL_OUTPUT_DIR, exist_ok=True)

        # تهيئة طرق التحميل في مدير الأولوية
        self._initialize_priority_manager()

        logger.info("🎵 تم تهيئة مدير تحميل الصوت المحسن")
        
    def _initialize_download_methods(self) -> Dict[AudioDownloadMethod, DownloadMethodConfig]:
        """تهيئة طرق التحميل"""
        methods = {}
        
        # 1. Apify - الأولوية الأولى
        if BotConfig.APIFY_API_TOKEN:
            methods[AudioDownloadMethod.APIFY] = DownloadMethodConfig(
                name="Apify YouTube Downloader",
                enabled=True,
                priority=1,
                cost_per_use=BotConfig.APIFY_COST_PER_RUN,
                monthly_limit=int(BotConfig.APIFY_FREE_CREDIT_MONTHLY / BotConfig.APIFY_COST_PER_RUN),
                timeout_seconds=300,
                quality_score=0.95,
                supports_formats=["mp3", "wav", "m4a"]
            )
            
        # 2. youtube-dl - الأولوية الثانية
        if BotConfig.YOUTUBE_DL_ENABLED:
            methods[AudioDownloadMethod.YOUTUBE_DL] = DownloadMethodConfig(
                name="youtube-dl",
                enabled=True,
                priority=2,
                cost_per_use=0.0,  # مجاني
                monthly_limit=999999,  # بلا حدود
                timeout_seconds=600,
                quality_score=0.90,
                supports_formats=["mp3", "wav", "m4a", "ogg"]
            )
            
        # 3. yt-dlp - بديل محسن لـ youtube-dl
        methods[AudioDownloadMethod.YT_DLP] = DownloadMethodConfig(
            name="yt-dlp",
            enabled=True,
            priority=3,
            cost_per_use=0.0,
            monthly_limit=999999,
            timeout_seconds=600,
            quality_score=0.92,
            supports_formats=["mp3", "wav", "m4a", "ogg"]
        )
        
        # 4. PyTube - الطريقة الحالية كبديل
        methods[AudioDownloadMethod.PYTUBE] = DownloadMethodConfig(
            name="PyTube (Legacy)",
            enabled=True,
            priority=4,
            cost_per_use=0.0,
            monthly_limit=999999,
            timeout_seconds=300,
            quality_score=0.75,
            supports_formats=["mp4", "webm"]
        )
        
        # 5. YouTube API - البديل الأخير
        methods[AudioDownloadMethod.YOUTUBE_API] = DownloadMethodConfig(
            name="YouTube API (Legacy)",
            enabled=True,
            priority=5,
            cost_per_use=0.0,
            monthly_limit=999999,
            timeout_seconds=300,
            quality_score=0.70,
            supports_formats=["mp4", "webm"]
        )
        
        return methods

    def _initialize_priority_manager(self):
        """تهيئة طرق التحميل في مدير الأولوية"""
        for method, config in self.download_methods.items():
            self.priority_manager.initialize_method(
                method.value,
                config.monthly_limit,
                0,  # لا يوجد حد يومي حالياً
                config.cost_per_use
            )
        
    def _load_usage_stats(self) -> Dict[str, Dict]:
        """تحميل إحصائيات الاستخدام"""
        try:
            stats_file = "cache/audio_download_usage.json"
            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في تحميل إحصائيات التحميل: {e}")
        
        return {}
        
    def _save_usage_stats(self):
        """حفظ إحصائيات الاستخدام"""
        try:
            os.makedirs("cache", exist_ok=True)
            stats_file = "cache/audio_download_usage.json"
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_stats, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"⚠️ خطأ في حفظ إحصائيات التحميل: {e}")
            
    def _get_monthly_usage(self, method: AudioDownloadMethod) -> int:
        """الحصول على الاستخدام الشهري لطريقة معينة"""
        current_month = datetime.now().strftime("%Y-%m")
        method_name = method.value
        
        if method_name not in self.usage_stats:
            self.usage_stats[method_name] = {}
            
        return self.usage_stats[method_name].get(current_month, 0)
        
    def _update_usage(self, method: AudioDownloadMethod, cost: float = 1.0):
        """تحديث إحصائيات الاستخدام"""
        current_month = datetime.now().strftime("%Y-%m")
        method_name = method.value
        
        if method_name not in self.usage_stats:
            self.usage_stats[method_name] = {}
            
        current_usage = self.usage_stats[method_name].get(current_month, 0)
        self.usage_stats[method_name][current_month] = current_usage + cost
        
        self._save_usage_stats()
        
    def _get_available_methods(self, quality: str = "medium") -> List[Tuple[AudioDownloadMethod, DownloadMethodConfig]]:
        """الحصول على الطرق المتاحة مرتبة حسب الأولوية"""
        available = []
        
        for method, config in self.download_methods.items():
            if not config.enabled:
                continue
                
            # فحص الحد الشهري
            monthly_usage = self._get_monthly_usage(method)
            if monthly_usage >= config.monthly_limit:
                logger.debug(f"⚠️ {config.name}: تجاوز الحد الشهري ({monthly_usage}/{config.monthly_limit})")
                continue
                
            available.append((method, config))
            
        # ترتيب حسب الأولوية
        available.sort(key=lambda x: x[1].priority)
        
        return available
        
    async def download_audio(self, video_id: str, video_url: str = "", 
                           quality: str = "medium", format: str = "mp3") -> DownloadResult:
        """تحميل الصوت باستخدام أفضل طريقة متاحة"""
        
        start_time = time.time()
        
        if not video_url:
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            
        logger.info(f"🎵 بدء تحميل الصوت: {video_id} - جودة: {quality}")

        # الحصول على أفضل طريقة من مدير الأولوية
        best_method = self.priority_manager.get_best_method(quality)

        if not best_method:
            logger.warning("⚠️ لا توجد طرق تحميل متاحة")
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="none", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message="لا توجد طرق تحميل متاحة"
            )

        # الحصول على قائمة الطرق مرتبة حسب الأولوية
        fallback_methods = self.priority_manager.fallback_chain
            
        # محاولة كل طريقة حسب الأولوية
        for method_name in fallback_methods:
            if not self.priority_manager._is_method_available(method_name):
                continue

            try:
                method_enum = AudioDownloadMethod(method_name)
                config = self.download_methods.get(method_enum)

                if not config or not config.enabled:
                    continue

                logger.info(f"🔄 محاولة {config.name}...")
                method_start_time = time.time()

                result = await self._download_with_method(
                    method_enum, config, video_id, video_url, quality, format
                )

                method_processing_time = time.time() - method_start_time

                # تسجيل النتيجة في مدير الأولوية
                self.priority_manager.record_download_result(
                    method_name, result.success, method_processing_time,
                    config.cost_per_use, result.error_message or ""
                )

                if result.success:
                    # تحديث الاستخدام
                    self._update_usage(method_enum, config.cost_per_use)

                    processing_time = time.time() - start_time
                    result.processing_time = processing_time

                    logger.info(f"✅ نجح {config.name} - {result.file_size} بايت في {processing_time:.1f}ث")
                    return result
                else:
                    logger.warning(f"❌ فشل {config.name}: {result.error_message}")

            except Exception as e:
                logger.error(f"❌ خطأ في {method_name}: {e}")
                # تسجيل الخطأ في مدير الأولوية
                self.priority_manager.record_download_result(
                    method_name, False, 0.0, 0.0, str(e)
                )
                continue
                
        # إذا فشلت جميع الطرق
        processing_time = time.time() - start_time
        return DownloadResult(
            success=False, audio_data=None, file_path=None,
            method_used="all_failed", processing_time=processing_time,
            file_size=0, audio_format=format, quality=quality,
            error_message="فشلت جميع طرق التحميل"
        )
        
    async def _download_with_method(self, method: AudioDownloadMethod, config: DownloadMethodConfig,
                                  video_id: str, video_url: str, quality: str, format: str) -> DownloadResult:
        """تحميل الصوت باستخدام طريقة محددة"""
        
        if method == AudioDownloadMethod.APIFY:
            return await self._download_with_apify(config, video_id, video_url, quality, format)
        elif method == AudioDownloadMethod.YOUTUBE_DL:
            return await self._download_with_youtube_dl(config, video_id, video_url, quality, format)
        elif method == AudioDownloadMethod.YT_DLP:
            return await self._download_with_youtube_dl(config, video_id, video_url, quality, format)  # نفس youtube-dl
        elif method == AudioDownloadMethod.PYTUBE:
            return await self._download_with_pytube(config, video_id, video_url, quality, format)
        elif method == AudioDownloadMethod.YOUTUBE_API:
            return await self._download_with_youtube_api(config, video_id, video_url, quality, format)
        else:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used=method.value, processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"طريقة غير مدعومة: {method.value}"
            )
            
    def get_download_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التحميل"""
        # الحصول على إحصائيات من مدير الأولوية
        priority_stats = self.priority_manager.get_download_statistics()

        # إضافة معلومات إضافية
        enhanced_stats = {
            **priority_stats,
            "apify_stats": apify_downloader.get_usage_statistics() if apify_downloader.is_available() else None,
            "youtube_dl_stats": youtube_dl_downloader.get_tool_info(),
            "system_info": {
                "temp_directory": self.temp_dir,
                "cache_directory": self.cache_dir,
                "supported_formats": ["mp3", "wav", "m4a", "ogg"],
                "supported_qualities": ["high", "medium", "low"]
            }
        }

        return enhanced_stats

    def get_method_availability(self) -> Dict[str, bool]:
        """فحص توفر جميع طرق التحميل"""
        return {
            "apify": apify_downloader.is_available(),
            "youtube_dl": youtube_dl_downloader.is_available(),
            "yt_dlp": youtube_dl_downloader.yt_dlp_available,
            "ffmpeg": youtube_dl_downloader.ffmpeg_available
        }

    def reset_method_health(self, method_name: str = None):
        """إعادة تعيين صحة طرق التحميل"""
        if method_name:
            self.priority_manager.reset_method_health(method_name)
        else:
            # إعادة تعيين جميع الطرق
            for method in self.priority_manager.health_data.keys():
                self.priority_manager.reset_method_health(method)

        logger.info("🔄 تم إعادة تعيين صحة طرق التحميل")

    async def _download_with_apify(self, config: DownloadMethodConfig, video_id: str,
                                 video_url: str, quality: str, format: str) -> DownloadResult:
        """تحميل الصوت باستخدام Apify API"""
        try:
            # استخدام واجهة Apify الجديدة
            apify_result = await apify_downloader.download_audio(video_url, video_id, quality, format)

            if apify_result.success:
                # تحميل الملف من الرابط
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        apify_result.download_url,
                        timeout=aiohttp.ClientTimeout(total=300)
                    ) as response:

                        if response.status == 200:
                            audio_data = await response.read()

                            # حفظ الملف مؤقتاً
                            temp_file = os.path.join(self.temp_dir, f"{video_id}_apify.{format}")
                            with open(temp_file, 'wb') as f:
                                f.write(audio_data)

                            return DownloadResult(
                                success=True, audio_data=audio_data, file_path=temp_file,
                                method_used="apify", processing_time=apify_result.processing_time,
                                file_size=len(audio_data), audio_format=format, quality=quality,
                                duration_seconds=apify_result.duration_seconds
                            )
                        else:
                            return DownloadResult(
                                success=False, audio_data=None, file_path=None,
                                method_used="apify", processing_time=apify_result.processing_time,
                                file_size=0, audio_format=format, quality=quality,
                                error_message=f"فشل تحميل الملف من Apify: {response.status}"
                            )
            else:
                return DownloadResult(
                    success=False, audio_data=None, file_path=None,
                    method_used="apify", processing_time=apify_result.processing_time,
                    file_size=0, audio_format=format, quality=quality,
                    error_message=apify_result.error_message
                )

        except Exception as e:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="apify", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"خطأ في Apify: {str(e)}"
            )

    async def _download_with_youtube_dl(self, config: DownloadMethodConfig, video_id: str,
                                      video_url: str, quality: str, format: str) -> DownloadResult:
        """تحميل الصوت باستخدام youtube-dl أو yt-dlp"""
        try:
            # استخدام واجهة YouTube-DL الجديدة
            ytdl_result = await youtube_dl_downloader.download_audio(video_url, video_id, quality, format)

            if ytdl_result.success:
                return DownloadResult(
                    success=True, audio_data=ytdl_result.audio_data, file_path=ytdl_result.file_path,
                    method_used=ytdl_result.tool_used, processing_time=ytdl_result.processing_time,
                    file_size=ytdl_result.file_size, audio_format=ytdl_result.format, quality=ytdl_result.quality,
                    duration_seconds=ytdl_result.duration_seconds
                )
            else:
                return DownloadResult(
                    success=False, audio_data=None, file_path=None,
                    method_used=ytdl_result.tool_used, processing_time=ytdl_result.processing_time,
                    file_size=0, audio_format=format, quality=quality,
                    error_message=ytdl_result.error_message
                )

        except Exception as e:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="youtube_dl", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"خطأ في YouTube-DL: {str(e)}"
            )

    async def _download_with_pytube(self, config: DownloadMethodConfig, video_id: str,
                                  video_url: str, quality: str, format: str) -> DownloadResult:
        """تحميل الصوت باستخدام PyTube (الطريقة القديمة)"""
        try:
            # هنا يمكن إضافة الكود للطريقة القديمة أو استيرادها
            # من الوحدة الموجودة
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="pytube", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message="PyTube غير مطبق حالياً - استخدم youtube-dl بدلاً منه"
            )

        except Exception as e:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="pytube", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"خطأ في PyTube: {str(e)}"
            )

    async def _download_with_youtube_api(self, config: DownloadMethodConfig, video_id: str,
                                       video_url: str, quality: str, format: str) -> DownloadResult:
        """تحميل الصوت باستخدام YouTube API (الطريقة القديمة)"""
        try:
            # هنا يمكن إضافة الكود للطريقة القديمة أو استيرادها
            # من الوحدة الموجودة
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="youtube_api", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message="YouTube API غير مطبق حالياً - استخدم youtube-dl بدلاً منه"
            )

        except Exception as e:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="youtube_api", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"خطأ في YouTube API: {str(e)}"
            )

    async def _get_apify_results(self, session: aiohttp.ClientSession, headers: Dict,
                               run_id: str, video_id: str, format: str, quality: str) -> DownloadResult:
        """الحصول على نتائج Apify وتحميل الملف"""
        try:
            # الحصول على النتائج
            results_url = f"{BotConfig.APIFY_API_URL}/actor-runs/{run_id}/dataset/items"

            async with session.get(
                results_url,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as results_response:

                if results_response.status != 200:
                    error_text = await results_response.text()
                    return DownloadResult(
                        success=False, audio_data=None, file_path=None,
                        method_used="apify", processing_time=0.0, file_size=0,
                        audio_format=format, quality=quality,
                        error_message=f"فشل الحصول على نتائج Apify: {error_text}"
                    )

                results = await results_response.json()

                if not results or len(results) == 0:
                    return DownloadResult(
                        success=False, audio_data=None, file_path=None,
                        method_used="apify", processing_time=0.0, file_size=0,
                        audio_format=format, quality=quality,
                        error_message="لا توجد نتائج من Apify"
                    )

                # البحث عن رابط التحميل
                download_url = None
                for result in results:
                    if "downloadUrl" in result:
                        download_url = result["downloadUrl"]
                        break
                    elif "url" in result:
                        download_url = result["url"]
                        break

                if not download_url:
                    return DownloadResult(
                        success=False, audio_data=None, file_path=None,
                        method_used="apify", processing_time=0.0, file_size=0,
                        audio_format=format, quality=quality,
                        error_message="لم يتم العثور على رابط التحميل في نتائج Apify"
                    )

                # تحميل الملف الصوتي
                async with session.get(
                    download_url,
                    timeout=aiohttp.ClientTimeout(total=300)
                ) as download_response:

                    if download_response.status == 200:
                        audio_data = await download_response.read()

                        # حفظ الملف مؤقتاً
                        temp_file = os.path.join(self.temp_dir, f"{video_id}_apify.{format}")
                        with open(temp_file, 'wb') as f:
                            f.write(audio_data)

                        return DownloadResult(
                            success=True, audio_data=audio_data, file_path=temp_file,
                            method_used="apify", processing_time=0.0, file_size=len(audio_data),
                            audio_format=format, quality=quality
                        )
                    else:
                        error_text = await download_response.text()
                        return DownloadResult(
                            success=False, audio_data=None, file_path=None,
                            method_used="apify", processing_time=0.0, file_size=0,
                            audio_format=format, quality=quality,
                            error_message=f"فشل تحميل الملف من Apify: {error_text}"
                        )

        except Exception as e:
            return DownloadResult(
                success=False, audio_data=None, file_path=None,
                method_used="apify", processing_time=0.0, file_size=0,
                audio_format=format, quality=quality,
                error_message=f"خطأ في الحصول على نتائج Apify: {str(e)}"
            )


# إنشاء مثيل عام للاستخدام
enhanced_audio_downloader = EnhancedAudioDownloader()
