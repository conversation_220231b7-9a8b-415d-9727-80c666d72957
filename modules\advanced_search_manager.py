#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير البحث المتقدم مع نظام البدائل التلقائي
يدير البحث عبر محركات متعددة مع التبديل التلقائي عند الفشل
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from enum import Enum
import random
import hashlib

from .logger import logger
from .database import db
from .api_key_manager import ApiKeyManager
from .tavily_search import tavily_search
from .serpapi_search import serpapi_search
from .search1api_search import search1api_search
try:
    from .smart_search_manager import smart_search_manager
except ImportError:
    smart_search_manager = None
from config.settings import BotConfig

class SearchEngine(Enum):
    """محركات البحث المتاحة مرتبة حسب الأولوية"""
    TAVILY = "tavily"                    # الأولوية الأولى - بحث عميق مع AI
    SEARCH1API = "search1api"            # الأولوية الثانية - بديل ممتاز لـ Tavily
    BRAVE_SEARCH = "brave_search"        # الأولوية الثالثة - بحث مجاني قوي
    SERPAPI = "serpapi"                  # الأولوية الرابعة - بحث Google متقدم
    SCRAPERAPI = "scraperapi"            # الأولوية الخامسة - استخراج متقدم
    ZYTE = "zyte"                        # الأولوية السادسة - استخراج موزع
    CONTEXTUALWEB = "contextualweb"      # الأولوية السابعة - بحث أخبار
    SERPER_DEV = "serper_dev"            # الأولوية الثامنة - AI للبحث
    GOOGLE_CUSTOM = "google_custom"      # الأولوية الأخيرة - احتياطي

class AdvancedSearchManager:
    """مدير البحث المتقدم مع نظام البدائل الذكي"""
    
    def __init__(self):
        """تهيئة مدير البحث المتقدم"""
        self.search_engines = {}
        self.engine_status = {}
        self.fallback_chain = []
        self.usage_stats = {}
        self.cache = {}
        self.cache_duration = 3600  # ساعة واحدة
        
        # تهيئة محركات البحث
        self._initialize_search_engines()
        
        # إعداد سلسلة البدائل
        self._setup_fallback_chain()
        
        # تحميل إحصائيات الاستخدام
        self._load_usage_stats()
        
        logger.info(f"🚀 تم تهيئة مدير البحث المتقدم مع {len(self.search_engines)} محرك")
    
    def _initialize_search_engines(self):
        """تهيئة جميع محركات البحث المتاحة"""
        
        # 1. Tavily (الأولوية الأولى)
        if tavily_search.enabled:
            self.search_engines[SearchEngine.TAVILY] = {
                'instance': tavily_search,
                'enabled': True,
                'priority': 1,
                'daily_limit': 35,
                'monthly_limit': 1000,
                'quality_score': 9.5,
                'cost_per_request': 0,
                'success_rate': 0.95
            }
        
        # 2. Search1API (بديل ممتاز لـ Tavily)
        if search1api_search.enabled:
            self.search_engines[SearchEngine.SEARCH1API] = {
                'instance': search1api_search,
                'enabled': True,
                'priority': 2,
                'daily_limit': 200,
                'monthly_limit': 6000,
                'quality_score': 9.0,
                'cost_per_request': 0,
                'success_rate': 0.92
            }

        # 3. Brave Search (مجاني وقوي)
        if hasattr(BotConfig, 'BRAVE_SEARCH_KEY') and BotConfig.BRAVE_SEARCH_KEY:
            self.search_engines[SearchEngine.BRAVE_SEARCH] = {
                'enabled': True,
                'priority': 3,
                'daily_limit': 5000,  # حد سخي لـ Brave
                'monthly_limit': 150000,
                'quality_score': 8.0,
                'cost_per_request': 0,
                'success_rate': 0.85
            }

        # 4. SerpAPI
        if serpapi_search.enabled:
            self.search_engines[SearchEngine.SERPAPI] = {
                'instance': serpapi_search,
                'enabled': True,
                'priority': 4,
                'daily_limit': 100,
                'monthly_limit': 5000,
                'quality_score': 8.5,
                'cost_per_request': 0,
                'success_rate': 0.90
            }
        
        # 5. ScraperAPI
        if hasattr(BotConfig, 'SCRAPERAPI_KEYS') and any(BotConfig.SCRAPERAPI_KEYS):
            self.search_engines[SearchEngine.SCRAPERAPI] = {
                'enabled': True,
                'priority': 5,
                'daily_limit': 33,
                'monthly_limit': 1000,
                'quality_score': 7.5,
                'cost_per_request': 0,
                'success_rate': 0.80
            }

        # 6. Zyte
        if hasattr(BotConfig, 'ZYTE_API_KEYS') and any(BotConfig.ZYTE_API_KEYS):
            self.search_engines[SearchEngine.ZYTE] = {
                'enabled': True,
                'priority': 6,
                'daily_limit': 33,
                'monthly_limit': 1000,
                'quality_score': 7.0,
                'cost_per_request': 0,
                'success_rate': 0.75
            }
        
        # 7. ContextualWeb
        if hasattr(BotConfig, 'CONTEXTUALWEB_KEYS') and any(BotConfig.CONTEXTUALWEB_KEYS):
            self.search_engines[SearchEngine.CONTEXTUALWEB] = {
                'enabled': True,
                'priority': 7,
                'daily_limit': 1000,
                'monthly_limit': 30000,
                'quality_score': 6.5,
                'cost_per_request': 0,
                'success_rate': 0.85
            }

        # 8. Serper.dev
        if hasattr(BotConfig, 'SERPER_DEV_KEYS') and any(BotConfig.SERPER_DEV_KEYS):
            self.search_engines[SearchEngine.SERPER_DEV] = {
                'enabled': True,
                'priority': 8,
                'daily_limit': 3,
                'monthly_limit': 100,
                'quality_score': 8.0,
                'cost_per_request': 0,
                'success_rate': 0.88
            }

        # 9. Google Custom Search
        if hasattr(BotConfig, 'GOOGLE_CUSTOM_SEARCH_KEYS') and any(BotConfig.GOOGLE_CUSTOM_SEARCH_KEYS):
            self.search_engines[SearchEngine.GOOGLE_CUSTOM] = {
                'enabled': True,
                'priority': 9,
                'daily_limit': 100,
                'monthly_limit': 3000,
                'quality_score': 6.0,
                'cost_per_request': 0,
                'success_rate': 0.70
            }
        
        # تهيئة حالة المحركات
        for engine in self.search_engines:
            self.engine_status[engine] = {
                'is_active': True,
                'consecutive_failures': 0,
                'last_success': None,
                'last_failure': None,
                'daily_usage': 0,
                'monthly_usage': 0
            }
    
    def _setup_fallback_chain(self):
        """إعداد سلسلة البدائل حسب الأولوية والجودة"""
        # ترتيب المحركات حسب الأولوية
        available_engines = [
            (engine, config) for engine, config in self.search_engines.items()
            if config['enabled'] and self.engine_status[engine]['is_active']
        ]
        
        # ترتيب حسب الأولوية ثم الجودة
        available_engines.sort(key=lambda x: (x[1]['priority'], -x[1]['quality_score']))
        
        self.fallback_chain = [engine for engine, _ in available_engines]
        
        logger.info(f"🔄 سلسلة البدائل: {[engine.value for engine in self.fallback_chain]}")
    
    async def advanced_search(self, query: str, max_results: int = 10, search_type: str = "gaming_news") -> List[Dict]:
        """
        البحث المتقدم مع نظام البدائل التلقائي
        """
        logger.info(f"🔍 بدء البحث المتقدم عن: '{query}'")
        
        # فحص التخزين المؤقت أولاً
        cache_key = self._generate_cache_key(query, max_results, search_type)
        cached_result = self._get_cached_result(cache_key)
        if cached_result:
            logger.info(f"📦 استخدام نتائج مخزنة مؤقتاً لـ: {query}")
            return cached_result
        
        # محاولة البحث عبر سلسلة البدائل
        for engine in self.fallback_chain:
            try:
                # فحص حالة المحرك
                if not self._is_engine_available(engine):
                    logger.warning(f"⚠️ {engine.value} غير متاح - الانتقال للبديل التالي")
                    continue
                
                # تنفيذ البحث
                results = await self._execute_search_with_engine(engine, query, max_results, search_type)
                
                if results and len(results) > 0:
                    # نجح البحث
                    self._record_success(engine)
                    self._cache_result(cache_key, results)
                    
                    logger.info(f"✅ نجح البحث باستخدام {engine.value}: {len(results)} نتيجة")
                    return results
                else:
                    # فشل البحث
                    self._record_failure(engine)
                    logger.warning(f"❌ فشل البحث في {engine.value} - محاولة البديل التالي")
                    
            except Exception as e:
                # خطأ في البحث
                self._record_failure(engine)
                logger.error(f"❌ خطأ في {engine.value}: {e}")
                continue
        
        # فشل جميع المحركات
        logger.error(f"❌ فشل البحث في جميع المحركات لـ: {query}")
        return []
    
    def _is_engine_available(self, engine: SearchEngine) -> bool:
        """فحص توفر المحرك للاستخدام"""
        if engine not in self.search_engines or engine not in self.engine_status:
            return False
        
        config = self.search_engines[engine]
        status = self.engine_status[engine]
        
        # فحص التفعيل
        if not config['enabled'] or not status['is_active']:
            return False
        
        # فحص الفشل المتتالي
        if status['consecutive_failures'] >= 3:
            logger.warning(f"⚠️ {engine.value} معطل مؤقتاً بسبب الفشل المتتالي")
            return False
        
        # فحص الحدود اليومية
        if status['daily_usage'] >= config['daily_limit']:
            logger.warning(f"⚠️ تجاوز الحد اليومي لـ {engine.value}")
            return False
        
        return True
    
    async def _execute_search_with_engine(self, engine: SearchEngine, query: str, max_results: int, search_type: str) -> List[Dict]:
        """تنفيذ البحث باستخدام محرك محدد"""
        
        if engine == SearchEngine.TAVILY:
            return await self._search_with_tavily(query, max_results)
        elif engine == SearchEngine.SEARCH1API:
            return await self._search_with_search1api(query, max_results)
        elif engine == SearchEngine.BRAVE_SEARCH:
            return await self._search_with_brave_search(query, max_results)
        elif engine == SearchEngine.SERPAPI:
            return await self._search_with_serpapi(query, max_results)
        elif engine == SearchEngine.SCRAPERAPI:
            if smart_search_manager:
                return await smart_search_manager._search_with_scraperapi(query, max_results)
            return []
        elif engine == SearchEngine.ZYTE:
            if smart_search_manager:
                return await smart_search_manager._search_with_zyte(query, max_results)
            return []
        elif engine == SearchEngine.CONTEXTUALWEB:
            if smart_search_manager:
                return await smart_search_manager._search_with_contextualweb(query, max_results)
            return []
        elif engine == SearchEngine.SERPER_DEV:
            if smart_search_manager:
                return await smart_search_manager._search_with_serper_dev(query, max_results)
            return []
        elif engine == SearchEngine.GOOGLE_CUSTOM:
            if smart_search_manager:
                return await smart_search_manager._search_with_google_custom(query, max_results)
            return []
        
        return []
    
    async def _search_with_tavily(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام Tavily"""
        results = await tavily_search.search(
            query=f"{query} gaming news",
            search_depth="advanced",
            max_results=max_results
        )
        return self._normalize_results(results, "Tavily")

    async def _search_with_search1api(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام Search1API"""
        results = await search1api_search.search_gaming_content(
            query=query,
            max_results=max_results
        )
        return self._normalize_results(results, "Search1API")

    async def _search_with_brave_search(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام Brave Search"""
        try:
            # استخدام advanced_web_scraper للوصول لـ Brave Search
            if smart_search_manager:
                from .advanced_web_scraper import advanced_web_scraper
                results = await advanced_web_scraper._search_with_brave(query, max_results)
                return self._normalize_results(results, "Brave Search")
            return []
        except Exception as e:
            logger.error(f"❌ خطأ في Brave Search: {e}")
            return []
    
    async def _search_with_serpapi(self, query: str, max_results: int) -> List[Dict]:
        """البحث باستخدام SerpAPI"""
        results = await serpapi_search.search(
            query=f"{query} gaming news",
            num_results=max_results
        )
        return self._normalize_results(results, "SerpAPI")
    
    def _normalize_results(self, results: List[Dict], source: str) -> List[Dict]:
        """توحيد تنسيق النتائج"""
        normalized = []
        for result in results:
            try:
                normalized_result = {
                    'title': result.get('title', ''),
                    'content': result.get('content', ''),
                    'summary': result.get('summary', result.get('content', '')[:200] + "..."),
                    'url': result.get('url', ''),
                    'source': source,
                    'published_date': result.get('published_date', datetime.now()),
                    'search_keyword': result.get('search_keyword', ''),
                    'relevance_score': result.get('relevance_score', 5.0),
                    'search_engine': source,
                    'extraction_method': f'{source.lower()}_search'
                }
                normalized.append(normalized_result)
            except Exception as e:
                logger.debug(f"خطأ في توحيد النتيجة: {e}")
                continue
        
        return normalized

    def _record_success(self, engine: SearchEngine):
        """تسجيل نجاح البحث"""
        if engine in self.engine_status:
            self.engine_status[engine]['consecutive_failures'] = 0
            self.engine_status[engine]['last_success'] = datetime.now()
            self.engine_status[engine]['daily_usage'] += 1
            self.engine_status[engine]['monthly_usage'] += 1

    def _record_failure(self, engine: SearchEngine):
        """تسجيل فشل البحث"""
        if engine in self.engine_status:
            self.engine_status[engine]['consecutive_failures'] += 1
            self.engine_status[engine]['last_failure'] = datetime.now()

            # تعطيل مؤقت بعد 3 فشل متتالي
            if self.engine_status[engine]['consecutive_failures'] >= 3:
                self.engine_status[engine]['is_active'] = False
                logger.warning(f"⚠️ تم تعطيل {engine.value} مؤقتاً بسبب الفشل المتتالي")

    def _generate_cache_key(self, query: str, max_results: int, search_type: str) -> str:
        """إنشاء مفتاح للتخزين المؤقت"""
        cache_data = f"{query}_{max_results}_{search_type}"
        return f"advanced_search_{hashlib.md5(cache_data.encode()).hexdigest()}"

    def _get_cached_result(self, cache_key: str) -> Optional[List[Dict]]:
        """الحصول على نتيجة مخزنة مؤقتاً"""
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if datetime.now() - cached_data['timestamp'] < timedelta(seconds=self.cache_duration):
                return cached_data['results']
            else:
                del self.cache[cache_key]
        return None

    def _cache_result(self, cache_key: str, results: List[Dict]):
        """حفظ النتيجة في التخزين المؤقت"""
        self.cache[cache_key] = {
            'results': results,
            'timestamp': datetime.now()
        }

        # تنظيف التخزين المؤقت القديم
        if len(self.cache) > 100:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]

    def _load_usage_stats(self):
        """تحميل إحصائيات الاستخدام"""
        try:
            saved_stats = db.get_api_usage_stats('advanced_search_manager')
            if saved_stats:
                for engine_name, stats in saved_stats.items():
                    try:
                        engine = SearchEngine(engine_name)
                        if engine in self.engine_status:
                            self.engine_status[engine].update(stats)
                    except ValueError:
                        continue
        except Exception as e:
            logger.debug(f"لم يتم تحميل إحصائيات الاستخدام: {e}")

    def _save_usage_stats(self):
        """حفظ إحصائيات الاستخدام"""
        try:
            stats_to_save = {}
            for engine, status in self.engine_status.items():
                stats_to_save[engine.value] = status

            db.save_api_usage_stats('advanced_search_manager', stats_to_save)
        except Exception as e:
            logger.debug(f"فشل في حفظ إحصائيات الاستخدام: {e}")

    def get_engine_status(self) -> Dict:
        """الحصول على حالة جميع المحركات"""
        status_report = {}

        for engine, config in self.search_engines.items():
            status = self.engine_status[engine]
            status_report[engine.value] = {
                'enabled': config['enabled'],
                'is_active': status['is_active'],
                'priority': config['priority'],
                'quality_score': config['quality_score'],
                'daily_usage': status['daily_usage'],
                'daily_limit': config['daily_limit'],
                'consecutive_failures': status['consecutive_failures'],
                'last_success': status['last_success'].isoformat() if status['last_success'] else None,
                'last_failure': status['last_failure'].isoformat() if status['last_failure'] else None,
                'success_rate': config.get('success_rate', 0.0)
            }

        return status_report

    async def test_all_engines(self) -> Dict:
        """اختبار جميع محركات البحث"""
        test_results = {}
        test_query = "gaming news test"

        for engine in self.search_engines:
            try:
                logger.info(f"🧪 اختبار {engine.value}...")

                start_time = datetime.now()
                results = await self._execute_search_with_engine(engine, test_query, 3, "test")
                end_time = datetime.now()

                response_time = (end_time - start_time).total_seconds()

                test_results[engine.value] = {
                    'status': 'success' if results else 'failed',
                    'results_count': len(results) if results else 0,
                    'response_time': response_time,
                    'message': f'تم العثور على {len(results)} نتيجة' if results else 'لم يتم العثور على نتائج'
                }

                if results:
                    self._record_success(engine)
                else:
                    self._record_failure(engine)

            except Exception as e:
                test_results[engine.value] = {
                    'status': 'error',
                    'results_count': 0,
                    'response_time': 0,
                    'message': f'خطأ: {str(e)}'
                }
                self._record_failure(engine)

        return test_results

    def reset_engine_status(self, engine: SearchEngine = None):
        """إعادة تعيين حالة المحركات"""
        if engine:
            if engine in self.engine_status:
                self.engine_status[engine]['is_active'] = True
                self.engine_status[engine]['consecutive_failures'] = 0
                logger.info(f"🔄 تم إعادة تعيين حالة {engine.value}")
        else:
            for eng in self.engine_status:
                self.engine_status[eng]['is_active'] = True
                self.engine_status[eng]['consecutive_failures'] = 0
            logger.info("🔄 تم إعادة تعيين حالة جميع المحركات")

        # إعادة إعداد سلسلة البدائل
        self._setup_fallback_chain()

# إنشاء مثيل عام
advanced_search_manager = AdvancedSearchManager()
